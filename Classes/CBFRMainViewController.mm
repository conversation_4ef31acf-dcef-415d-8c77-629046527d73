#import "CBFRMainViewController.h"
#import "CBFRMainView.h"
#import "MHAboutView.h"
#import "CBFRMHAboutViewController2.h"
#import "CBFRUIView+EAFeatureGuideView.h"
#import "CBFRbwCameraAppDelegate.h"
#include <UserMessagingPlatform/UserMessagingPlatform.h>
#define _filter_num 11
char * _filter[_filter_num] = {"Original", "Black&White", "Sepia", "Burn", "Atmosphere", "Fog", "Freeze", "Lava", "Metal", "Rainbow", "Water"};
int _current_filter = 0;
@class CBFRMainViewController;
CBFRMainViewController *g_mvc;
int g_iCanSave = 0;
int g_iSourceType;
id g_sender;
UIImage* fixImage(UIImage * image)
{
	int width = image.size.width;
	int height = image.size.height;
	CGSize size = CGSizeMake(width, height);
	CGRect imageRect;
	if(image.imageOrientation==UIImageOrientationUp 
	   || image.imageOrientation==UIImageOrientationDown) 
	{
		imageRect = CGRectMake(0, 0, width, height); 
	}
	else 
	{
		imageRect = CGRectMake(0, 0, height, width); 
	}
	UIGraphicsBeginImageContext(size);
	CGContextRef context = UIGraphicsGetCurrentContext();
	CGContextSaveGState(context);
	CGContextTranslateCTM(context, 0, height);
	CGContextScaleCTM(context, 1.0, -1.0);
	if(image.imageOrientation==UIImageOrientationLeft) 
	{
		CGContextRotateCTM(context, M_PI / 2);
		CGContextTranslateCTM(context, 0, -width);
	}
	else if(image.imageOrientation==UIImageOrientationRight) 
	{
		CGContextRotateCTM(context, - M_PI / 2);
		CGContextTranslateCTM(context, -height, 0);
	} 
	else if(image.imageOrientation==UIImageOrientationUp) 
	{
	}
	else if(image.imageOrientation==UIImageOrientationDown) 
	{
		CGContextTranslateCTM(context, width, height);
		CGContextRotateCTM(context, M_PI);
	}
	CGContextDrawImage(context, imageRect, image.CGImage);
	CGContextRestoreGState(context);
	UIImage *img = UIGraphicsGetImageFromCurrentImageContext();
	UIGraphicsEndImageContext();
	return img;
}
CGImageRef createGrayCopy(CGImageRef source)
{
	int width = CGImageGetWidth(source);
	int height = CGImageGetHeight(source);
	CGColorSpaceRef colorSpace = CGColorSpaceCreateDeviceGray();
	CGContextRef context = CGBitmapContextCreate (nil,
												  width,
												  height,
												  8,      
												  0,
												  colorSpace,
												  kCGImageAlphaNone);
	CGColorSpaceRelease(colorSpace);
	if (context == NULL) {
		return nil;
	}
	CGContextDrawImage(context, CGRectMake(0, 0, width, height), source);
	CGImageRef grayImage = CGBitmapContextCreateImage(context);
	CGContextRelease(context);
	return grayImage;
}
void ByteMe(long &TempVar)
{
	if (TempVar > 255)
		TempVar = 255;
	if (TempVar < 0)
		TempVar = 0;
}
float Maximum(float & rR, float & rG, float & rB)
{
	float _Maximum;
	if (rR > rG)
	{
		if (rR > rB)
			_Maximum = rR;
		else
			_Maximum = rB;
	}
	else
	{
		if (rB > rG)
			_Maximum = rB;
		else
			_Maximum = rG;
	}
	return _Maximum;
}
float Minimum(float &rR , float& rG, float &rB )
{
	float _Minimum;
	if (rR < rG)
	{
		if (rR < rB)
		{
			_Minimum = rR;
		}
		else
		{
			_Minimum = rB;
		}
	}
	else
	{
		if (rB < rG)
			_Minimum = rB;
		else
			_Minimum = rG;
	}
	return _Minimum;
}
void tRGBToHSL(long & R, long & G, long & B, float & h , float & s, float & L)
{
	float Max, Min, delta;
	float rR, rG, rB;
	rR = R / 255.0; rG = G / 255.0; rB = B / 255.0;
	Max = Maximum(rR, rG, rB);
	Min = Minimum(rR, rG, rB);
	L = (Max + Min) / 2;
	if (Max == Min)
	{
		s = 0;
		h = 0;
	}
	else
	{
		if (L <= 0.5)
		{
			s = (Max - Min) / (Max + Min);
		}
		else
		{
			s = (Max - Min) / (2 - Max - Min);
		}
		delta = Max - Min;
		if (rR == Max)
		{
			h = (rG - rB) / delta;
		}
		else if( rG == Max)
		{
			h = 2 + (rB - rR) / delta;
		}
		else if( rB == Max)
		{
			h = 4 + (rR - rG) / delta;
		}
	}
}
void tHSLToRGB(float & h, float & s, float & L, long & R, long & G, long & B)
{
	float rR, rG, rB;
	float Min, Max;
	if (s == 0)
	{
		rR = L; rG = L; rB = L;
	}
	else
	{
		if (L <= 0.5)
		{
			Min = L * (1 - s);
		}
		else
		{
			Min = L - s * (1 - L);
		}
		Max = 2 * L - Min;
		if (h < 1)
		{
			rR = Max;
			if (h < 0)
			{
				rG = Min;
				rB = rG - h * (Max - Min);
			}
			else
			{
				rB = Min;
				rG = h * (Max - Min) + rB;
			}
		}	
		else if (h < 3)
		{
			rG = Max;
			if (h < 2)
			{
				rB = Min;
				rR = rB - (h - 2) * (Max - Min);
			}
			else
			{
				rR = Min;
				rB = (h - 2) * (Max - Min) + rR;
			}
		}
		else
		{
			rB = Max;
			if (h < 4) 
			{
				rR = Min;
				rG = rR - (h - 4) * (Max - Min);
			}
			else
			{
				rG = Min;
				rR = (h - 4) * (Max - Min) + rG;
			}
		}
	}	
	R = rR * 255; G = rG * 255; B = rB * 255;
}
@interface ImagePicker : UIImagePickerController <UIImagePickerControllerDelegate, UINavigationControllerDelegate>
@end
@implementation ImagePicker
- (id) init
{
	if (!(self = [super init])) return self;
	if ([UIImagePickerController isSourceTypeAvailable:g_iSourceType])	self.sourceType = g_iSourceType;
	self.delegate = self;
	return self;
}
- (void)imagePickerController:(UIImagePickerController *)picker didFinishPickingImage:(UIImage *)image editingInfo:(NSDictionary *)editingInfo
{
    [self cbfr_processImage:image];
    [self dismissModalViewControllerAnimated:YES];
    if ([[UIDevice currentDevice] userInterfaceIdiom] == UIUserInterfaceIdiomPad) {
        [g_mvc cbfr_DismissPopver];
    }
}
-(void)cbfr_processImage:(UIImage*)image
{
    UIImage *image0 = fixImage(image);
    if (g_mvc.cbfr_original)
    {
        CGImageRelease(g_mvc.cbfr_original);
        g_mvc.cbfr_original = nil;
    }
    g_mvc.cbfr_original = CGImageCreateCopy(image0.CGImage);
    if (g_mvc.current)
    {
        CGImageRelease(g_mvc.current);
        g_mvc.current = nil;
    }
    g_mvc.current = CGImageCreateCopy(g_mvc.cbfr_original);
    g_mvc._flash = 0;
    [g_mvc processAction:(SEL)@selector(cbfr_applyFilter)];
}
- (void)imagePickerControllerDidCancel:(UIImagePickerController *)picker
{
    [self dismissModalViewControllerAnimated:YES];
}
@end
@implementation CBFRMainViewController
@synthesize myAlert;
@synthesize cbfr_original;
@synthesize current;
@synthesize _flash;
-(IBAction)cbfr_showAbout
{
    CBFRMHAboutViewController2 *about = [[CBFRMHAboutViewController2 alloc] init];
    UINavigationController * con1 = [[UINavigationController alloc] initWithRootViewController:about];
    [self presentModalViewController:con1 animated:YES];
}
-(void)cbfr_DismissPopver
{
    [popover dismissPopoverAnimated:YES];
    popover = nil;
}
- (void)actionSheet:(UIActionSheet *)actionSheet clickedButtonAtIndex:(NSInteger)buttonIndex
{
#ifdef CBFR_KW_DEBUG
	printf("User Pressed Button %d\n", buttonIndex + 1);
#endif
	int tag = actionSheet.tag;
	if (tag == 1)
	{
		if (buttonIndex == 0)
		{
            [self performSelector:@selector(cbfr_loadFromCamera) withObject:nil afterDelay:0.1];
		}
		else if (buttonIndex == 1)
		{
            [self performSelector:@selector(cbfr_loadFromLibrary) withObject:nil afterDelay:0.1];
		}
	}
    if (tag == 2)
    {
        if (buttonIndex == 0)
        {
            [self performSelector:@selector(cbfr_showAbout) withObject:nil afterDelay:0.1];
        }
        else if (buttonIndex == 1)
        {
            [self performSelector:@selector(cbfr_share2) withObject:nil afterDelay:0.1];
        }
        else if (buttonIndex == 2)
        {
            NSTimeInterval cbfr_delay = 0.1;
            if ([[UIDevice currentDevice] userInterfaceIdiom] == UIUserInterfaceIdiomPad) {
                cbfr_delay = 0.5;
            }
            [self performSelector:@selector(cbfr_presentPrivacyOptionsForm) withObject:nil afterDelay:cbfr_delay];
        }
    }
}
- (void) doAction: (NSString*) sel1
{
	SEL s = NSSelectorFromString(sel1);
	[self performSelector:s];
	[self cbfr_hideMyAlert];
}
- (void) processAction: (SEL) sel1
{
	[self cbfr_showMyAlert];
	NSString *str1 = NSStringFromSelector(sel1);
	[self performSelector:@selector(doAction:) withObject:str1 afterDelay:0.1];
}
-(void)cbfr_showMyAlert
{
	assert(!myAlert);
	myAlert = [[UIAlertView alloc] initWithTitle: nil
										 message: @"Processing..."
										delegate: self
							   cancelButtonTitle: nil
							   otherButtonTitles: nil];
	UIActivityIndicatorView *activityView = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleWhite];
	activityView.frame = CGRectMake(120.f, 48.0f, 37.0f, 37.0f);
	[myAlert addSubview:activityView];
	[activityView startAnimating];
	[myAlert show];
}
-(void)cbfr_hideMyAlert
{
	assert(myAlert);
	[myAlert dismissWithClickedButtonIndex:0 animated:YES];
	myAlert = nil;
}
- (IBAction)cbfr_convert2BW
{
	if (!self.current)
		return;
	UIAlertView *alert= nil; 
	{		
		alert = [[UIAlertView alloc] initWithTitle: nil
										   message: @"Processing..."
										  delegate: self
								 cancelButtonTitle: nil
								 otherButtonTitles: nil];
		UIActivityIndicatorView *activityView = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleWhite];
		activityView.frame = CGRectMake(120.f, 48.0f, 37.0f, 37.0f);
		[alert addSubview:activityView];
		[activityView startAnimating];
		[alert show];
	}
	CGImageRef image0 = [self cbfr_convertImage2BW:self.current];
	CGImageRelease(self.current);
	self.current = nil;
	self.current = image0;
	[self cbfr_showImage:self.current];
	g_mvc._flash = 0;
	[alert dismissWithClickedButtonIndex:0 animated:YES];
	alert = nil;
}
- (CGImageRef)cbfr_flashImage:(CGImageRef)abgrImageRef level:(int)f{
    CFMutableDataRef abgrData = CFDataCreateMutableCopy(0, 0, CGDataProviderCopyData(CGImageGetDataProvider(abgrImageRef)));
    UInt8 *pixelData=(UInt8 *)CFDataGetMutableBytePtr(abgrData);
	int length = CFDataGetLength(abgrData);
	int tmpByte = 0;
	{
	for (int index = 0; index < length; index+= 4) {
		for(int i = 1;  i < 4; i++)
		{
		tmpByte = pixelData[index + i] + f;
		if (tmpByte < 0)
			tmpByte = 0;
		if (tmpByte > 255)
			tmpByte = 255;
		pixelData[index + i] = tmpByte;
		}
	}
	}
	size_t width = CGImageGetWidth(abgrImageRef);
	size_t height = CGImageGetHeight(abgrImageRef);
	size_t bitsPerComponent = CGImageGetBitsPerComponent(abgrImageRef);
	size_t bitsPerPixel = CGImageGetBitsPerPixel(abgrImageRef);
	size_t bytesPerRow = CGImageGetBytesPerRow(abgrImageRef);
	CGColorSpaceRef colorspace = CGImageGetColorSpace(abgrImageRef);
	CGBitmapInfo bitmapInfo = CGImageGetBitmapInfo(abgrImageRef);
	CFDataRef argbData = CFDataCreate(NULL, pixelData, length);
	CGDataProviderRef provider = CGDataProviderCreateWithCFData(argbData);
	CGImageRef argbImageRef = CGImageCreate(width, height, bitsPerComponent, bitsPerPixel, bytesPerRow, 
											colorspace, bitmapInfo, provider, NULL, true, kCGRenderingIntentDefault);
	CFRelease(abgrData);
	CFRelease(argbData);
	CGDataProviderRelease(provider);
	return argbImageRef;
}
- (CGImageRef)cbfr_sepiaImage:(CGImageRef)abgrImageRef depth:(int)d{
    CFMutableDataRef abgrData = CFDataCreateMutableCopy(0, 0, CGDataProviderCopyData(CGImageGetDataProvider(abgrImageRef)));
    UInt8 *pixelData=(UInt8 *)CFDataGetMutableBytePtr(abgrData);
	int length = CFDataGetLength(abgrData);
	CGImageAlphaInfo ai = CGImageGetAlphaInfo(abgrImageRef);
	if (ai == kCGImageAlphaPremultipliedFirst)	
	{
		unsigned int *pd7;
		unsigned int i8;
		for (int i7 = 0; i7 < length; i7 += 4) {
			pd7 = (unsigned int*)(pixelData + i7) ;
			i8 = *pd7;
			*pd7 = (i8 << 8) | (i8 >> 24);
		}
	}
	{
		for (int index = 0; index < length; index+= 4) {
			{
				int color2 = (pixelData[index + 1] + pixelData[index + 2] + pixelData[index + 3]) / 3;
				int rr = color2 + (d *2);
				int gg = color2 + d;
				if (rr > 255)
					rr = 255;
				if(gg > 255)
					gg = 255;
				pixelData[index + 1] = color2;
				pixelData[index + 2] = gg;
				pixelData[index + 3] = rr;
			}
		}
	}
	if (ai == kCGImageAlphaPremultipliedFirst)
	{
		unsigned int *pd7;
		unsigned int i8;
		for (int i7 = 0; i7 < length; i7 += 4) {
			pd7 = (unsigned int*)(pixelData + i7) ;
			i8 = *pd7;
			*pd7 = (i8 >> 8) | (i8 << 24);
		}
	}
	size_t width = CGImageGetWidth(abgrImageRef);
	size_t height = CGImageGetHeight(abgrImageRef);
	size_t bitsPerComponent = CGImageGetBitsPerComponent(abgrImageRef);
	size_t bitsPerPixel = CGImageGetBitsPerPixel(abgrImageRef);
	size_t bytesPerRow = CGImageGetBytesPerRow(abgrImageRef);
	CGColorSpaceRef colorspace = CGImageGetColorSpace(abgrImageRef);
	CGBitmapInfo bitmapInfo = CGImageGetBitmapInfo(abgrImageRef);
	CFDataRef argbData = CFDataCreate(NULL, pixelData, length);
	CGDataProviderRef provider = CGDataProviderCreateWithCFData(argbData);
	CGImageRef argbImageRef = CGImageCreate(width, height, bitsPerComponent, bitsPerPixel, bytesPerRow, 
											colorspace, bitmapInfo, provider, NULL, true, kCGRenderingIntentDefault);
	CFRelease(abgrData);
	CFRelease(argbData);
	CGDataProviderRelease(provider);
	return argbImageRef;
}
- (CGImageRef)cbfr_oceanImage:(CGImageRef)abgrImageRef{
    CFMutableDataRef abgrData = CFDataCreateMutableCopy(0, 0, CGDataProviderCopyData(CGImageGetDataProvider(abgrImageRef)));
    UInt8 *pixelData=(UInt8 *)CFDataGetMutableBytePtr(abgrData);
	int length = CFDataGetLength(abgrData);
	{
		int r, g, b;
		for (int index = 0; index < length; index+= 4) {
			{
				int color2 = (pixelData[index + 1] + pixelData[index + 2] + pixelData[index + 3]) / 3;
				r = color2 / 3;
				g = color2;
				b = color2 * 3;
				if (b > 255)
					b = 255;
				pixelData[index + 1] = b;
				pixelData[index + 2] = g;
				pixelData[index + 3] = r;
			}
		}
	}
	size_t width = CGImageGetWidth(abgrImageRef);
	size_t height = CGImageGetHeight(abgrImageRef);
	size_t bitsPerComponent = CGImageGetBitsPerComponent(abgrImageRef);
	size_t bitsPerPixel = CGImageGetBitsPerPixel(abgrImageRef);
	size_t bytesPerRow = CGImageGetBytesPerRow(abgrImageRef);
	CGColorSpaceRef colorspace = CGImageGetColorSpace(abgrImageRef);
	CGBitmapInfo bitmapInfo = CGImageGetBitmapInfo(abgrImageRef);
	CFDataRef argbData = CFDataCreate(NULL, pixelData, length);
	CGDataProviderRef provider = CGDataProviderCreateWithCFData(argbData);
	CGImageRef argbImageRef = CGImageCreate(width, height, bitsPerComponent, bitsPerPixel, bytesPerRow, 
											colorspace, bitmapInfo, provider, NULL, true, kCGRenderingIntentDefault);
	CFRelease(abgrData);
	CFRelease(argbData);
	CGDataProviderRelease(provider);
	return argbImageRef;
}
- (CGImageRef)cbfr_burnImage:(CGImageRef)abgrImageRef{
    CFMutableDataRef abgrData = CFDataCreateMutableCopy(0, 0, CGDataProviderCopyData(CGImageGetDataProvider(abgrImageRef)));
    UInt8 *pixelData=(UInt8 *)CFDataGetMutableBytePtr(abgrData);
	int length = CFDataGetLength(abgrData);
	CGImageAlphaInfo ai = CGImageGetAlphaInfo(abgrImageRef);
	if (ai == kCGImageAlphaPremultipliedFirst)	
	{
		unsigned int *pd7;
		unsigned int i8;
		for (int i7 = 0; i7 < length; i7 += 4) {
			pd7 = (unsigned int*)(pixelData + i7) ;
			i8 = *pd7;
			*pd7 = (i8 << 8) | (i8 >> 24);
		}
	}
	{
		int r, g, b;
		for (int index = 0; index < length; index+= 4) {
			{
				int color2 = (pixelData[index + 1] + pixelData[index + 2] + pixelData[index + 3]) / 3;
				r = color2 * 3;
				g = color2;
				b = color2 / 3;
				if (r > 255)
					r = 255;
				pixelData[index + 1] = b;
				pixelData[index + 2] = g;
				pixelData[index + 3] = r;
			}
		}
	}
	if (ai == kCGImageAlphaPremultipliedFirst)
	{
		unsigned int *pd7;
		unsigned int i8;
		for (int i7 = 0; i7 < length; i7 += 4) {
			pd7 = (unsigned int*)(pixelData + i7) ;
			i8 = *pd7;
			*pd7 = (i8 >> 8) | (i8 << 24);
		}
	}
	size_t width = CGImageGetWidth(abgrImageRef);
	size_t height = CGImageGetHeight(abgrImageRef);
	size_t bitsPerComponent = CGImageGetBitsPerComponent(abgrImageRef);
	size_t bitsPerPixel = CGImageGetBitsPerPixel(abgrImageRef);
	size_t bytesPerRow = CGImageGetBytesPerRow(abgrImageRef);
	CGColorSpaceRef colorspace = CGImageGetColorSpace(abgrImageRef);
	CGBitmapInfo bitmapInfo = CGImageGetBitmapInfo(abgrImageRef);
	CFDataRef argbData = CFDataCreate(NULL, pixelData, length);
	CGDataProviderRef provider = CGDataProviderCreateWithCFData(argbData);
	CGImageRef argbImageRef = CGImageCreate(width, height, bitsPerComponent, bitsPerPixel, bytesPerRow, 
											colorspace, bitmapInfo, provider, NULL, true, kCGRenderingIntentDefault);
	CFRelease(abgrData);
	CFRelease(argbData);
	CGDataProviderRelease(provider);
	return argbImageRef;
}
- (CGImageRef)cbfr_atmosphereImage:(CGImageRef)abgrImageRef{
    CFMutableDataRef abgrData = CFDataCreateMutableCopy(0, 0, CGDataProviderCopyData(CGImageGetDataProvider(abgrImageRef)));
    UInt8 *pixelData=(UInt8 *)CFDataGetMutableBytePtr(abgrData);
	int length = CFDataGetLength(abgrData);
	CGImageAlphaInfo ai = CGImageGetAlphaInfo(abgrImageRef);
	if (ai == kCGImageAlphaPremultipliedFirst)	
	{
		unsigned int *pd7;
		unsigned int i8;
		for (int i7 = 0; i7 < length; i7 += 4) {
			pd7 = (unsigned int*)(pixelData + i7) ;
			i8 = *pd7;
			*pd7 = (i8 << 8) | (i8 >> 24);
		}
	}
	{
		int r, g, b;
		int r1, g1, b1;
		for (int index = 0; index < length; index+= 4) {
			{
				b = pixelData[index + 1];
				g = pixelData[index + 2];
				r = pixelData[index + 3];
				r1 = (g + b) / 2;
				g1 = (r + b) / 2;
				b1 = (r + g) / 2;
				pixelData[index + 1] = b1;
				pixelData[index + 2] = g1;
				pixelData[index + 3] = r1;
			}
		}
	}
	if (ai == kCGImageAlphaPremultipliedFirst)
	{
		unsigned int *pd7;
		unsigned int i8;
		for (int i7 = 0; i7 < length; i7 += 4) {
			pd7 = (unsigned int*)(pixelData + i7) ;
			i8 = *pd7;
			*pd7 = (i8 >> 8) | (i8 << 24);
		}
	}
	size_t width = CGImageGetWidth(abgrImageRef);
	size_t height = CGImageGetHeight(abgrImageRef);
	size_t bitsPerComponent = CGImageGetBitsPerComponent(abgrImageRef);
	size_t bitsPerPixel = CGImageGetBitsPerPixel(abgrImageRef);
	size_t bytesPerRow = CGImageGetBytesPerRow(abgrImageRef);
	CGColorSpaceRef colorspace = CGImageGetColorSpace(abgrImageRef);
	CGBitmapInfo bitmapInfo = CGImageGetBitmapInfo(abgrImageRef);
	CFDataRef argbData = CFDataCreate(NULL, pixelData, length);
	CGDataProviderRef provider = CGDataProviderCreateWithCFData(argbData);
	CGImageRef argbImageRef = CGImageCreate(width, height, bitsPerComponent, bitsPerPixel, bytesPerRow, 
											colorspace, bitmapInfo, provider, NULL, true, kCGRenderingIntentDefault);
	CFRelease(abgrData);
	CFRelease(argbData);
	CGDataProviderRelease(provider);
	return argbImageRef;
}
- (CGImageRef)cbfr_fogImage:(CGImageRef)abgrImageRef{
    CFMutableDataRef abgrData = CFDataCreateMutableCopy(0, 0, CGDataProviderCopyData(CGImageGetDataProvider(abgrImageRef)));
    UInt8 *pixelData=(UInt8 *)CFDataGetMutableBytePtr(abgrData);
	int length = CFDataGetLength(abgrData);
	CGImageAlphaInfo ai = CGImageGetAlphaInfo(abgrImageRef);
	if (ai == kCGImageAlphaPremultipliedFirst)	
	{
		unsigned int *pd7;
		unsigned int i8;
		for (int i7 = 0; i7 < length; i7 += 4) {
			pd7 = (unsigned int*)(pixelData + i7) ;
			i8 = *pd7;
			*pd7 = (i8 << 8) | (i8 >> 24);
		}
	}
	{
		int FogLimit = 40;
		int LookUp[256];
		for(int x = 0; x < 256; x++)
		{
			if (x > 127)
			{
				LookUp[x] = x - FogLimit;
				if( LookUp[x] < 127)
				{
					LookUp[x] = 127;
				}
			}
			else
			{
				LookUp[x] = x + FogLimit;
				if(LookUp[x] > 127)
				{
					LookUp[x] = 127;
				}
			}
		}
		int r, g, b;
		int r1, g1, b1;
		for (int index = 0; index < length; index+= 4) {
			{
				b = pixelData[index + 1];
				g = pixelData[index + 2];
				r = pixelData[index + 3];
				r1 = LookUp[r];
				g1 = LookUp[g];
				b1 = LookUp[b];
				pixelData[index + 1] = b1;
				pixelData[index + 2] = g1;
				pixelData[index + 3] = r1;
			}
		}
	}
	if (ai == kCGImageAlphaPremultipliedFirst)
	{
		unsigned int *pd7;
		unsigned int i8;
		for (int i7 = 0; i7 < length; i7 += 4) {
			pd7 = (unsigned int*)(pixelData + i7) ;
			i8 = *pd7;
			*pd7 = (i8 >> 8) | (i8 << 24);
		}
	}
	size_t width = CGImageGetWidth(abgrImageRef);
	size_t height = CGImageGetHeight(abgrImageRef);
	size_t bitsPerComponent = CGImageGetBitsPerComponent(abgrImageRef);
	size_t bitsPerPixel = CGImageGetBitsPerPixel(abgrImageRef);
	size_t bytesPerRow = CGImageGetBytesPerRow(abgrImageRef);
	CGColorSpaceRef colorspace = CGImageGetColorSpace(abgrImageRef);
	CGBitmapInfo bitmapInfo = CGImageGetBitmapInfo(abgrImageRef);
	CFDataRef argbData = CFDataCreate(NULL, pixelData, length);
	CGDataProviderRef provider = CGDataProviderCreateWithCFData(argbData);
	CGImageRef argbImageRef = CGImageCreate(width, height, bitsPerComponent, bitsPerPixel, bytesPerRow, 
											colorspace, bitmapInfo, provider, NULL, true, kCGRenderingIntentDefault);
	CFRelease(abgrData);
	CFRelease(argbData);
	CGDataProviderRelease(provider);
	return argbImageRef;
}
- (CGImageRef)cbfr_freezeImage:(CGImageRef)abgrImageRef{
    CFMutableDataRef abgrData = CFDataCreateMutableCopy(0, 0, CGDataProviderCopyData(CGImageGetDataProvider(abgrImageRef)));
    UInt8 *pixelData=(UInt8 *)CFDataGetMutableBytePtr(abgrData);
	int length = CFDataGetLength(abgrData);
	CGImageAlphaInfo ai = CGImageGetAlphaInfo(abgrImageRef);
	if (ai == kCGImageAlphaPremultipliedFirst)	
	{
		unsigned int *pd7;
		unsigned int i8;
		for (int i7 = 0; i7 < length; i7 += 4) {
			pd7 = (unsigned int*)(pixelData + i7) ;
			i8 = *pd7;
			*pd7 = (i8 << 8) | (i8 >> 24);
		}
	}
	{
		int r, g, b;
		int r1, g1, b1;
		for (int index = 0; index < length; index+= 4) {
			{
				b = pixelData[index + 1];
				g = pixelData[index + 2];
				r = pixelData[index + 3];
				r1 = abs(r - b - g) * 1.5;
				g1 = abs(g - b - r1) * 1.5;
				b1 = abs(b - r1 -g1) * 1.5;
				if (r1 < 0)
					r1 = 0;
				if (r1 > 255)
					r1 = 255;
				if (g1 < 0)
					g1 = 0;
				if (g1 > 255)
					g1 = 255;
				if (b1 < 0)
					b1 = 0;
				if (b1 > 255)
					b1 = 255;
				pixelData[index + 1] = b1;
				pixelData[index + 2] = g1;
				pixelData[index + 3] = r1;
			}
		}
	}
	if (ai == kCGImageAlphaPremultipliedFirst)
	{
		unsigned int *pd7;
		unsigned int i8;
		for (int i7 = 0; i7 < length; i7 += 4) {
			pd7 = (unsigned int*)(pixelData + i7) ;
			i8 = *pd7;
			*pd7 = (i8 >> 8) | (i8 << 24);
		}
	}
	size_t width = CGImageGetWidth(abgrImageRef);
	size_t height = CGImageGetHeight(abgrImageRef);
	size_t bitsPerComponent = CGImageGetBitsPerComponent(abgrImageRef);
	size_t bitsPerPixel = CGImageGetBitsPerPixel(abgrImageRef);
	size_t bytesPerRow = CGImageGetBytesPerRow(abgrImageRef);
	CGColorSpaceRef colorspace = CGImageGetColorSpace(abgrImageRef);
	CGBitmapInfo bitmapInfo = CGImageGetBitmapInfo(abgrImageRef);
	CFDataRef argbData = CFDataCreate(NULL, pixelData, length);
	CGDataProviderRef provider = CGDataProviderCreateWithCFData(argbData);
	CGImageRef argbImageRef = CGImageCreate(width, height, bitsPerComponent, bitsPerPixel, bytesPerRow, 
											colorspace, bitmapInfo, provider, NULL, true, kCGRenderingIntentDefault);
	CFRelease(abgrData);
	CFRelease(argbData);
	CGDataProviderRelease(provider);
	return argbImageRef;
}
- (CGImageRef)cbfr_waterImage:(CGImageRef)abgrImageRef{
    CFMutableDataRef abgrData = CFDataCreateMutableCopy(0, 0, CGDataProviderCopyData(CGImageGetDataProvider(abgrImageRef)));
    UInt8 *pixelData=(UInt8 *)CFDataGetMutableBytePtr(abgrData);
	int length = CFDataGetLength(abgrData);
	CGImageAlphaInfo ai = CGImageGetAlphaInfo(abgrImageRef);
	if (ai == kCGImageAlphaPremultipliedFirst)	
	{
		unsigned int *pd7;
		unsigned int i8;
		for (int i7 = 0; i7 < length; i7 += 4) {
			pd7 = (unsigned int*)(pixelData + i7) ;
			i8 = *pd7;
			*pd7 = (i8 << 8) | (i8 >> 24);
		}
	}
	{
		int r, g, b;
		int r1, g1, b1;
		int Gray;
		for (int index = 0; index < length; index+= 4) {
			{
				b = pixelData[index + 1];
				g = pixelData[index + 2];
				r = pixelData[index + 3];
				Gray = (r + g + b) / 3;
				r1 = Gray - g - b;
				g1 = Gray - r1 - b;
				b1 = Gray - r1 - g1;
				if (r1 < 0)
					r1 = 0;
				if (r1 > 255)
					r1 = 255;
				if (g1 < 0)
					g1 = 0;
				if (g1 > 255)
					g1 = 255;
				if (b1 < 0)
					b1 = 0;
				if (b1 > 255)
					b1 = 255;
				pixelData[index + 1] = b1;
				pixelData[index + 2] = g1;
				pixelData[index + 3] = r1;
			}
		}
	}
	if (ai == kCGImageAlphaPremultipliedFirst)
	{
		unsigned int *pd7;
		unsigned int i8;
		for (int i7 = 0; i7 < length; i7 += 4) {
			pd7 = (unsigned int*)(pixelData + i7) ;
			i8 = *pd7;
			*pd7 = (i8 >> 8) | (i8 << 24);
		}
	}
	size_t width = CGImageGetWidth(abgrImageRef);
	size_t height = CGImageGetHeight(abgrImageRef);
	size_t bitsPerComponent = CGImageGetBitsPerComponent(abgrImageRef);
	size_t bitsPerPixel = CGImageGetBitsPerPixel(abgrImageRef);
	size_t bytesPerRow = CGImageGetBytesPerRow(abgrImageRef);
	CGColorSpaceRef colorspace = CGImageGetColorSpace(abgrImageRef);
	CGBitmapInfo bitmapInfo = CGImageGetBitmapInfo(abgrImageRef);
	CFDataRef argbData = CFDataCreate(NULL, pixelData, length);
	CGDataProviderRef provider = CGDataProviderCreateWithCFData(argbData);
	CGImageRef argbImageRef = CGImageCreate(width, height, bitsPerComponent, bitsPerPixel, bytesPerRow, 
											colorspace, bitmapInfo, provider, NULL, true, kCGRenderingIntentDefault);
	CFRelease(abgrData);
	CFRelease(argbData);
	CGDataProviderRelease(provider);
	return argbImageRef;
}
- (CGImageRef)cbfr_lavaImage:(CGImageRef)abgrImageRef{
    CFMutableDataRef abgrData = CFDataCreateMutableCopy(0, 0, CGDataProviderCopyData(CGImageGetDataProvider(abgrImageRef)));
    UInt8 *pixelData=(UInt8 *)CFDataGetMutableBytePtr(abgrData);
	int length = CFDataGetLength(abgrData);
	CGImageAlphaInfo ai = CGImageGetAlphaInfo(abgrImageRef);
	if (ai == kCGImageAlphaPremultipliedFirst)	
	{
		unsigned int *pd7;
		unsigned int i8;
		for (int i7 = 0; i7 < length; i7 += 4) {
			pd7 = (unsigned int*)(pixelData + i7) ;
			i8 = *pd7;
			*pd7 = (i8 << 8) | (i8 >> 24);
		}
	}
	{
		int r, g, b;
		int r1, g1, b1;
		for (int index = 0; index < length; index+= 4) {
			{
				int color2 = (pixelData[index + 1] + pixelData[index + 2] + pixelData[index + 3]) / 3;
				b = pixelData[index + 1];
				g = pixelData[index + 2];
				r = pixelData[index + 3];
				r1 =color2;
				g1 = abs(b - 128);
				b1 = abs(b - 128);
				pixelData[index + 1] = b1;
				pixelData[index + 2] = g1;
				pixelData[index + 3] = r1;
			}
		}
	}
	if (ai == kCGImageAlphaPremultipliedFirst)
	{
		unsigned int *pd7;
		unsigned int i8;
		for (int i7 = 0; i7 < length; i7 += 4) {
			pd7 = (unsigned int*)(pixelData + i7) ;
			i8 = *pd7;
			*pd7 = (i8 >> 8) | (i8 << 24);
		}
	}
	size_t width = CGImageGetWidth(abgrImageRef);
	size_t height = CGImageGetHeight(abgrImageRef);
	size_t bitsPerComponent = CGImageGetBitsPerComponent(abgrImageRef);
	size_t bitsPerPixel = CGImageGetBitsPerPixel(abgrImageRef);
	size_t bytesPerRow = CGImageGetBytesPerRow(abgrImageRef);
	CGColorSpaceRef colorspace = CGImageGetColorSpace(abgrImageRef);
	CGBitmapInfo bitmapInfo = CGImageGetBitmapInfo(abgrImageRef);
	CFDataRef argbData = CFDataCreate(NULL, pixelData, length);
	CGDataProviderRef provider = CGDataProviderCreateWithCFData(argbData);
	CGImageRef argbImageRef = CGImageCreate(width, height, bitsPerComponent, bitsPerPixel, bytesPerRow, 
											colorspace, bitmapInfo, provider, NULL, true, kCGRenderingIntentDefault);
	CFRelease(abgrData);
	CFRelease(argbData);
	CGDataProviderRelease(provider);
	return argbImageRef;
}
- (CGImageRef)cbfr_metalImage:(CGImageRef)abgrImageRef{
    CFMutableDataRef abgrData = CFDataCreateMutableCopy(0, 0, CGDataProviderCopyData(CGImageGetDataProvider(abgrImageRef)));
    UInt8 *pixelData=(UInt8 *)CFDataGetMutableBytePtr(abgrData);
	int length = CFDataGetLength(abgrData);
	CGImageAlphaInfo ai = CGImageGetAlphaInfo(abgrImageRef);
	if (ai == kCGImageAlphaPremultipliedFirst)	
	{
		unsigned int *pd7;
		unsigned int i8;
		for (int i7 = 0; i7 < length; i7 += 4) {
			pd7 = (unsigned int*)(pixelData + i7) ;
			i8 = *pd7;
			*pd7 = (i8 << 8) | (i8 >> 24);
		}
	}
	{
		int tr;
		int r, g, b;
		int gray;
		for (int index = 0; index < length; index+= 4) {
			{
				tr = pixelData[index + 3];
				r = abs(tr - 64);
				g = abs(r - 64);
				b = abs(g - 64);
				gray = (222 * r + 707 * g + 71 * b) / 1000;
				r = gray + 70;
				r = r + (((r - 128) * 100) / 100);
				g = gray + 65;
				g = g + (((g - 128) * 100) / 100);
				b = gray + 75;
				b = b + (((b - 128) * 100) / 100);
				if (r < 0)
					r = 0;
				if (r > 255)
					r = 255;
				if (g < 0)
					g = 0;
				if (g > 255)
					g = 255;
				if (b < 0)
					b = 0;
				if (b > 255)
					b = 255;
				pixelData[index + 1] = b;
				pixelData[index + 2] = g;
				pixelData[index + 3] = r;
			}
		}
	}
	if (ai == kCGImageAlphaPremultipliedFirst)
	{
		unsigned int *pd7;
		unsigned int i8;
		for (int i7 = 0; i7 < length; i7 += 4) {
			pd7 = (unsigned int*)(pixelData + i7) ;
			i8 = *pd7;
			*pd7 = (i8 >> 8) | (i8 << 24);
		}
	}
	size_t width = CGImageGetWidth(abgrImageRef);
	size_t height = CGImageGetHeight(abgrImageRef);
	size_t bitsPerComponent = CGImageGetBitsPerComponent(abgrImageRef);
	size_t bitsPerPixel = CGImageGetBitsPerPixel(abgrImageRef);
	size_t bytesPerRow = CGImageGetBytesPerRow(abgrImageRef);
	CGColorSpaceRef colorspace = CGImageGetColorSpace(abgrImageRef);
	CGBitmapInfo bitmapInfo = CGImageGetBitmapInfo(abgrImageRef);
	CFDataRef argbData = CFDataCreate(NULL, pixelData, length);
	CGDataProviderRef provider = CGDataProviderCreateWithCFData(argbData);
	CGImageRef argbImageRef = CGImageCreate(width, height, bitsPerComponent, bitsPerPixel, bytesPerRow, 
											colorspace, bitmapInfo, provider, NULL, true, kCGRenderingIntentDefault);
	CFRelease(abgrData);
	CFRelease(argbData);
	CGDataProviderRelease(provider);
	return argbImageRef;
}
- (CGImageRef)cbfr_rainbowImage:(CGImageRef)abgrImageRef{
	size_t width = CGImageGetWidth(abgrImageRef);
	size_t height = CGImageGetHeight(abgrImageRef);
    CFMutableDataRef abgrData = CFDataCreateMutableCopy(0, 0, CGDataProviderCopyData(CGImageGetDataProvider(abgrImageRef)));
    UInt8 *pixelData=(UInt8 *)CFDataGetMutableBytePtr(abgrData);
	int length = CFDataGetLength(abgrData);
	CGImageAlphaInfo ai = CGImageGetAlphaInfo(abgrImageRef);
	if (ai == kCGImageAlphaPremultipliedFirst)	
	{
		unsigned int *pd7;
		unsigned int i8;
		for (int i7 = 0; i7 < length; i7 += 4) {
			pd7 = (unsigned int*)(pixelData + i7) ;
			i8 = *pd7;
			*pd7 = (i8 << 8) | (i8 >> 24);
		}
	}
	{
		long R, G, B;
		float HH, SS, LL;
		float hVal;
		int x;
		float v1 = 0.9;
		for (int index = 0; index < length; index+= 4) 
		{
			x = index / 4 % width;
			hVal = (x * 1.0 / width) * 360.0;
			hVal = (hVal - 60) / 60;
			B = pixelData[index + 1];
			G = pixelData[index + 2];
			R = pixelData[index + 3];
			tRGBToHSL( R, G, B, HH, SS, LL);
			tHSLToRGB (hVal, v1, LL, R, G, B);
			pixelData[index + 1] = B;
			pixelData[index + 2] = G;
			pixelData[index + 3] = R;
		}
	}
	if (ai == kCGImageAlphaPremultipliedFirst)
	{
		unsigned int *pd7;
		unsigned int i8;
		for (int i7 = 0; i7 < length; i7 += 4) {
			pd7 = (unsigned int*)(pixelData + i7) ;
			i8 = *pd7;
			*pd7 = (i8 >> 8) | (i8 << 24);
		}
	}
	size_t bitsPerComponent = CGImageGetBitsPerComponent(abgrImageRef);
	size_t bitsPerPixel = CGImageGetBitsPerPixel(abgrImageRef);
	size_t bytesPerRow = CGImageGetBytesPerRow(abgrImageRef);
	CGColorSpaceRef colorspace = CGImageGetColorSpace(abgrImageRef);
	CGBitmapInfo bitmapInfo = CGImageGetBitmapInfo(abgrImageRef);
	CFDataRef argbData = CFDataCreate(NULL, pixelData, length);
	CGDataProviderRef provider = CGDataProviderCreateWithCFData(argbData);
	CGImageRef argbImageRef = CGImageCreate(width, height, bitsPerComponent, bitsPerPixel, bytesPerRow, 
											colorspace, bitmapInfo, provider, NULL, true, kCGRenderingIntentDefault);
	CFRelease(abgrData);
	CFRelease(argbData);
	CGDataProviderRelease(provider);
	return argbImageRef;
}
- (CGImageRef)cbfr_invertImage:(CGImageRef)abgrImageRef{
    CFMutableDataRef abgrData = CFDataCreateMutableCopy(0, 0, CGDataProviderCopyData(CGImageGetDataProvider(abgrImageRef)));
    UInt8 *pixelData=(UInt8 *)CFDataGetMutableBytePtr(abgrData);
	int length = CFDataGetLength(abgrData);
	{
		for (int index = 0; index < length; index+= 4) {
				pixelData[index + 1] = ~(pixelData[index + 1]);
				pixelData[index + 2] = ~pixelData[index + 2];
				pixelData[index + 3] = ~pixelData[index + 3];
			}
	}
	size_t width = CGImageGetWidth(abgrImageRef);
	size_t height = CGImageGetHeight(abgrImageRef);
	size_t bitsPerComponent = CGImageGetBitsPerComponent(abgrImageRef);
	size_t bitsPerPixel = CGImageGetBitsPerPixel(abgrImageRef);
	size_t bytesPerRow = CGImageGetBytesPerRow(abgrImageRef);
	CGColorSpaceRef colorspace = CGImageGetColorSpace(abgrImageRef);
	CGBitmapInfo bitmapInfo = CGImageGetBitmapInfo(abgrImageRef);
	CFDataRef argbData = CFDataCreate(NULL, pixelData, length);
	CGDataProviderRef provider = CGDataProviderCreateWithCFData(argbData);
	CGImageRef argbImageRef = CGImageCreate(width, height, bitsPerComponent, bitsPerPixel, bytesPerRow, 
											colorspace, bitmapInfo, provider, NULL, true, kCGRenderingIntentDefault);
	CFRelease(abgrData);
	CFRelease(argbData);
	CGDataProviderRelease(provider);
	return argbImageRef;
}
- (CGImageRef)cbfr_convertImage2BW:(CGImageRef)abgrImageRef{
    CFMutableDataRef abgrData = CFDataCreateMutableCopy(0, 0, CGDataProviderCopyData(CGImageGetDataProvider(abgrImageRef)));
    UInt8 *pixelData=(UInt8 *)CFDataGetMutableBytePtr(abgrData);
	int length = CFDataGetLength(abgrData);
	CGImageAlphaInfo ai = CGImageGetAlphaInfo(abgrImageRef);
	if (ai == kCGImageAlphaPremultipliedFirst)	
	{
		unsigned int *pd7;
		unsigned int i8;
		for (int i7 = 0; i7 < length; i7 += 4) {
			pd7 = (unsigned int*)(pixelData + i7) ;
			i8 = *pd7;
			*pd7 = (i8 << 8) | (i8 >> 24);
		}
	}
	int tmpByte = 0;
	{
		for (int index = 0; index < length; index+= 4) {
			tmpByte = .3 * pixelData[index + 3] + .59 * pixelData[index + 2] + .11 * pixelData[index + 1]; 
			pixelData[index + 1] = pixelData[index + 2] = pixelData[index + 3] = tmpByte;
		}
	}
	if (ai == kCGImageAlphaPremultipliedFirst)
	{
		unsigned int *pd7;
		unsigned int i8;
		for (int i7 = 0; i7 < length; i7 += 4) {
			pd7 = (unsigned int*)(pixelData + i7) ;
			i8 = *pd7;
			*pd7 = (i8 >> 8) | (i8 << 24);
		}
	}
	size_t width = CGImageGetWidth(abgrImageRef);
	size_t height = CGImageGetHeight(abgrImageRef);
	size_t bitsPerComponent = CGImageGetBitsPerComponent(abgrImageRef);
	size_t bitsPerPixel = CGImageGetBitsPerPixel(abgrImageRef);
	size_t bytesPerRow = CGImageGetBytesPerRow(abgrImageRef);
	CGColorSpaceRef colorspace = CGImageGetColorSpace(abgrImageRef);
	CGBitmapInfo bitmapInfo = CGImageGetBitmapInfo(abgrImageRef);
	CFDataRef argbData = CFDataCreate(NULL, pixelData, length);
	CGDataProviderRef provider = CGDataProviderCreateWithCFData(argbData);
	CGImageRef argbImageRef = CGImageCreate(width, height, bitsPerComponent, bitsPerPixel, bytesPerRow, 
											colorspace, bitmapInfo, provider, NULL, true, kCGRenderingIntentDefault);
	CFRelease(abgrData);
	CFRelease(argbData);
	CGDataProviderRelease(provider);
	return argbImageRef;
}
- (CGImageRef)cbfr_flipImage:(CGImageRef)abgrImageRef{
    CFMutableDataRef abgrData = CFDataCreateMutableCopy(0, 0, CGDataProviderCopyData(CGImageGetDataProvider(abgrImageRef)));
    UInt8 *pixelData=(UInt8 *)CFDataGetMutableBytePtr(abgrData);
	int length = CFDataGetLength(abgrData);
	size_t width = CGImageGetWidth(abgrImageRef);
	size_t height = CGImageGetHeight(abgrImageRef);
	size_t bitsPerComponent = CGImageGetBitsPerComponent(abgrImageRef);
	size_t bitsPerPixel = CGImageGetBitsPerPixel(abgrImageRef);
	size_t bytesPerRow = CGImageGetBytesPerRow(abgrImageRef);
	{
		int* pData = (int*)pixelData;
		int d1;
		int  *p1, *p2;
		for (int i = 0; i < height; i++) {
			for(int j = 1;  j <= width/2; j++)
			{
				p1 = pData + i *  bytesPerRow / 4 + j;
				p2 = pData + (i + 1) *  bytesPerRow / 4 - j;
				d1 = *p1;
				*p1 = *p2;
				*p2 = d1;
			}
		}
	}
	CGColorSpaceRef colorspace = CGImageGetColorSpace(abgrImageRef);
	CGBitmapInfo bitmapInfo = CGImageGetBitmapInfo(abgrImageRef);
	CFDataRef argbData = CFDataCreate(NULL, pixelData, length);
	CGDataProviderRef provider = CGDataProviderCreateWithCFData(argbData);
	CGImageRef argbImageRef = CGImageCreate(width, height, bitsPerComponent, bitsPerPixel, bytesPerRow, 
											colorspace, bitmapInfo, provider, NULL, true, kCGRenderingIntentDefault);
	CFRelease(abgrData);
	CFRelease(argbData);
	CGDataProviderRelease(provider);
	return argbImageRef;
}
- (void)image:(UIImage *)image didFinishSavingWithError:(NSError *)error contextInfo:(void *)contextInfo
{
	[myAlert dismissWithClickedButtonIndex:0 animated:YES];
	myAlert = nil;
}
- (IBAction)cbfr_flashPlus
{
	if (!self.current)
	{
		UIAlertView *alert1 = [[UIAlertView alloc] 
							   initWithTitle:@"Please take a photo first!" message:nil 
							   delegate:self cancelButtonTitle:nil
							   otherButtonTitles:@"OK", nil];
		[alert1 show];
		return;
	}
	_flash += 20;
	if (_flash > 128)
	{
		_flash = 128;
		return;
	}
	UIAlertView *alert= nil; 
	{		
		alert = [[UIAlertView alloc] initWithTitle: nil
											 message: @"Processing..."
											delegate: self
								   cancelButtonTitle: nil
								   otherButtonTitles: nil];
		UIActivityIndicatorView *activityView = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleWhite];
		activityView.frame = CGRectMake(120.f, 48.0f, 37.0f, 37.0f);
		[alert addSubview:activityView];
		[activityView startAnimating];
		[alert show];
	}
	CGImageRef img1 = [self cbfr_flashImage:self.current level:_flash];
	[self cbfr_showImage:img1];
	CGImageRelease(img1);
	[alert dismissWithClickedButtonIndex:0 animated:YES];
	alert = nil;
}
- (IBAction)cbfr_flashMinus
{
	if (!self.current)
	{
		UIAlertView *alert1 = [[UIAlertView alloc] 
							   initWithTitle:@"Please take a photo first!" message:nil 
							   delegate:self cancelButtonTitle:nil
							   otherButtonTitles:@"OK", nil];
		[alert1 show];
		return;
	}
	_flash -= 20;
	if (_flash < -128)
	{
		_flash = -128;
		return;
	}
	UIAlertView *alert = nil; 
	{		
		alert = [[UIAlertView alloc] initWithTitle: nil
											 message: @"Processing..."
											delegate: self
								   cancelButtonTitle: nil
								   otherButtonTitles: nil];
		UIActivityIndicatorView *activityView = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleWhite];
		activityView.frame = CGRectMake(120.f, 48.0f, 37.0f, 37.0f);
		[alert addSubview:activityView];
		[activityView startAnimating];
		[alert show];
	}
	CGImageRef img1 = [self cbfr_flashImage:self.current level:_flash];
	[self cbfr_showImage:img1];
	CGImageRelease(img1);
	[alert dismissWithClickedButtonIndex:0 animated:YES];
	alert = nil;
}
- (IBAction)flip
{
	if (!self.current)
		return;
	UIAlertView *alert= nil; 
	{		
		alert = [[UIAlertView alloc] initWithTitle: nil
										   message: @"Processing..."
										  delegate: self
								 cancelButtonTitle: nil
								 otherButtonTitles: nil];
		UIActivityIndicatorView *activityView = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleWhite];
		activityView.frame = CGRectMake(120.f, 48.0f, 37.0f, 37.0f);
		[alert addSubview:activityView];
		[activityView startAnimating];
		[alert show];
	}
	CGImageRef img1 = [self cbfr_flipImage:self.current];
	CGImageRelease(self.current);
	self.current = nil;
	self.current = img1;
	_flash = 0;
	[self cbfr_showImage:self.current];
	[alert dismissWithClickedButtonIndex:0 animated:YES];
	alert = nil;
}
- (IBAction)sepia
{
	if (!self.current)
		return;
	UIAlertView *alert= nil; 
	{		
		alert = [[UIAlertView alloc] initWithTitle: nil
										   message: @"Processing..."
										  delegate: self
								 cancelButtonTitle: nil
								 otherButtonTitles: nil];
		UIActivityIndicatorView *activityView = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleWhite];
		activityView.frame = CGRectMake(120.f, 48.0f, 37.0f, 37.0f);
		[alert addSubview:activityView];
		[activityView startAnimating];
		[alert show];
	}
	CGImageRef img1 = [self cbfr_sepiaImage: self.current depth:20];
	CGImageRelease(self.current);
	self.current = nil;
	self.current = img1;
	_flash = 0;
	[self cbfr_showImage:self.current];
	[alert dismissWithClickedButtonIndex:0 animated:YES];
	alert = nil;
}
- (IBAction)ocean
{
	if (!self.current)
		return;
	UIAlertView *alert= nil; 
	{		
		alert = [[UIAlertView alloc] initWithTitle: nil
										   message: @"Processing..."
										  delegate: self
								 cancelButtonTitle: nil
								 otherButtonTitles: nil];
		UIActivityIndicatorView *activityView = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleWhite];
		activityView.frame = CGRectMake(120.f, 48.0f, 37.0f, 37.0f);
		[alert addSubview:activityView];
		[activityView startAnimating];
		[alert show];
	}
	CGImageRef img1 = [self cbfr_oceanImage: self.current];
	CGImageRelease(self.current);
	self.current = nil;
	self.current = img1;
	_flash = 0;
	[self cbfr_showImage:self.current];
	[alert dismissWithClickedButtonIndex:0 animated:YES];
	alert = nil;
}
- (IBAction)burn
{
	if (!self.current)
		return;
	UIAlertView *alert= nil; 
	{		
		alert = [[UIAlertView alloc] initWithTitle: nil
										   message: @"Processing..."
										  delegate: self
								 cancelButtonTitle: nil
								 otherButtonTitles: nil];
		UIActivityIndicatorView *activityView = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleWhite];
		activityView.frame = CGRectMake(120.f, 48.0f, 37.0f, 37.0f);
		[alert addSubview:activityView];
		[activityView startAnimating];
		[alert show];
	}
	CGImageRef img1 = [self cbfr_burnImage: self.current];
	CGImageRelease(self.current);
	self.current = nil;
	self.current = img1;
	_flash = 0;
	[self cbfr_showImage:self.current];
	[alert dismissWithClickedButtonIndex:0 animated:YES];
	alert = nil;
}
- (IBAction)invert
{
	if (!self.current)
		return;
	UIAlertView *alert= nil; 
	{		
		alert = [[UIAlertView alloc] initWithTitle: nil
										   message: @"Processing..."
										  delegate: self
								 cancelButtonTitle: nil
								 otherButtonTitles: nil];
		UIActivityIndicatorView *activityView = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleWhite];
		activityView.frame = CGRectMake(120.f, 48.0f, 37.0f, 37.0f);
		[alert addSubview:activityView];
		[activityView startAnimating];
		[alert show];
	}
	CGImageRef img1 = [self cbfr_invertImage: self.current];
	CGImageRelease(self.current);
	self.current = nil;
	self.current = img1;
	_flash = 0;
	[self cbfr_showImage:self.current];
	[alert dismissWithClickedButtonIndex:0 animated:YES];
	alert = nil;
}
- (IBAction)cbfr_showOriginal
{
	if (!self.current)
	{
		UIAlertView *alert1 = [[UIAlertView alloc] 
							   initWithTitle:@"Please take a photo first!" message:nil 
							   delegate:self cancelButtonTitle:nil
							   otherButtonTitles:@"OK", nil];
		[alert1 show];
		return;
	}
	UIAlertView *alert1 = nil; 
	{		
		alert1 = [[UIAlertView alloc] initWithTitle: nil
											message: @"Processing..."
										   delegate: self
								  cancelButtonTitle: nil
								  otherButtonTitles: nil];
		UIActivityIndicatorView *activityView = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleWhite];
		activityView.frame = CGRectMake(120.f, 48.0f, 37.0f, 37.0f);
		[alert1 addSubview:activityView];
		[activityView startAnimating];
		[alert1 show];
	}
	_flash = 0;
	CGImageRelease(self.current);
	self.current = CGImageCreateCopy(self.cbfr_original);
	[self cbfr_showImage:self.current];
	[alert1 dismissWithClickedButtonIndex:0 animated:YES];
	alert1 = nil;
}
#define DOCSFOLDER [NSHomeDirectory() stringByAppendingPathComponent:@"Documents"]
- (IBAction)cbfr_delImage
{
}
- (IBAction)cbfr_showMenu
{
#ifdef CBFR_KW_DEBUG
    NSLog(@"UMPConsentInformation.sharedInstance.privacyOptionsRequirementStatus = %d", UMPConsentInformation.sharedInstance.privacyOptionsRequirementStatus);
#endif
    NSString * cbfrPS = @"Privacy Settings";
    if (UMPConsentInformation.sharedInstance.privacyOptionsRequirementStatus ==
           UMPPrivacyOptionsRequirementStatusRequired)
    {
    }
    else
    {
        cbfrPS = nil;
    }
    UIActionSheet *menu = [[UIActionSheet alloc]
                           initWithTitle: nil
                           delegate:self
                           cancelButtonTitle:@"Cancel"
                           destructiveButtonTitle:nil
                           otherButtonTitles:@"About", @"Share", cbfrPS, nil];
    menu.tag = 2;
    menu.actionSheetStyle = UIActionSheetStyleBlackOpaque;
    [menu showInView:self.view];
}
- (IBAction)cbfr_share2
{
    if (!g_iCanSave)
    {
        UIAlertView *alert1 = [[UIAlertView alloc]
                               initWithTitle:@"Please take a photo first!" message:nil
                               delegate:self cancelButtonTitle:nil
                               otherButtonTitles:@"OK", nil];
        alert1.backgroundColor = [UIColor blackColor];
        [alert1 show];
        return;
    }
    UIDevice *device = [UIDevice currentDevice];
    NSString *version = device.systemVersion;
    if ([version doubleValue] < 6.0) {
        UIAlertView * alertView = [[UIAlertView alloc] initWithTitle:@"This feature requires IOS6!" message:nil delegate:nil cancelButtonTitle:@"OK" otherButtonTitles:nil];
        [alertView show];
        return;
    }
    UIImage * image =     ((UIImageView *)([self.view viewWithTag:100])).image;
    NSArray* dataToShare = [NSArray arrayWithObjects:image,nil];
    UIActivityViewController* activityViewController =
    [[UIActivityViewController alloc] initWithActivityItems:dataToShare
                                      applicationActivities:nil];
    if ([activityViewController respondsToSelector:@selector(popoverPresentationController)])
    {
        activityViewController.popoverPresentationController.barButtonItem = _nav_bar.topItem.rightBarButtonItem;
        activityViewController.popoverPresentationController.sourceView = self.view;
    }
    [self presentViewController:activityViewController animated:YES completion:^{}];
}
- (IBAction)cbfr_saveToLibrary
{
	if (!g_iCanSave)
	{
		return;
	}
	if (myAlert==nil){		
		myAlert = [[UIAlertView alloc] initWithTitle: nil
											 message: @"Saving..."
											delegate: self
								   cancelButtonTitle: nil
								   otherButtonTitles: nil];
		UIActivityIndicatorView *activityView = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleWhite];
		activityView.frame = CGRectMake(120.f, 48.0f, 37.0f, 37.0f);
		[myAlert addSubview:activityView];
		[activityView startAnimating];
		[myAlert show];
	}
	UIImageWriteToSavedPhotosAlbum(	((UIImageView *)([self.view viewWithTag:100])).image, self, (SEL)@selector(image:didFinishSavingWithError:contextInfo:), nil); 
}
- (void)alertView:(UIAlertView *)alertView clickedButtonAtIndex:(NSInteger)buttonIndex
{
}
- (IBAction)cbfr_applyFilter
{
	_nav_bar.topItem.title = [NSString stringWithCString:_filter[_current_filter]];
	if (!self.current)
	{
		return;
	}
	CGImageRef img1;
	switch (_current_filter) {
		case 0:
			img1 = CGImageCreateCopy(self.current);
			break;
		case 1:
			img1 = [self cbfr_convertImage2BW:self.current];
			break;
		case 2:
		   img1 = [self cbfr_sepiaImage: self.current depth:20];
			break;
		case 3:
			img1 = [self cbfr_burnImage:self.current];
			break;
		case 4:
			img1 = [self cbfr_atmosphereImage:self.current];
			break;
		case 5:
			img1 = [self cbfr_fogImage:self.current];
			break;
		case 6:
			img1 = [self cbfr_freezeImage:self.current];
			break;
		case 7:
			img1 = [self cbfr_lavaImage:self.current];
			break;
		case 8:
			img1 = [self cbfr_metalImage:self.current];
			break;
		case 9:
			img1 = [self cbfr_rainbowImage:self.current];
			break;
		case 10:
			img1 = [self cbfr_waterImage:self.current];
			break;
		default:
			break;
	}
	[self cbfr_showImage:img1];
	CGImageRelease(img1);
	img1 = nil;
}
- (IBAction)cbfr_nextFilter
{
	_current_filter++;
	if (_current_filter > _filter_num - 1)
	{
		_current_filter = 0;
	}
	[self processAction:(SEL)@selector(cbfr_applyFilter)];
}
- (IBAction)cbfr_prevFilter
{
	_current_filter--;
	if (_current_filter < 0)
	{
		_current_filter = _filter_num - 1;
	}
	[self processAction:(SEL)@selector(cbfr_applyFilter)];
}
- (IBAction)cbfr_loadFromCamera
{
	if (![UIImagePickerController isSourceTypeAvailable:UIImagePickerControllerSourceTypeCamera])
	{
		UIAlertView *alert1 = [[UIAlertView alloc] 
								  initWithTitle:@"This feature requires an iPhone!" message:nil 
								  delegate:self cancelButtonTitle:nil
								  otherButtonTitles:@"OK", nil];
		[alert1 show];
		return;
	}
	g_iSourceType = UIImagePickerControllerSourceTypeCamera;
	[self presentModalViewController:[[ImagePicker alloc] init] animated:YES];
}
- (IBAction)cbfr_presentPrivacyOptionsForm
{
    [UMPConsentForm presentPrivacyOptionsFormFromViewController:self
                                              completionHandler:nil];
}
- (IBAction)cbfr_loadFromLibrary
{
	g_iSourceType = UIImagePickerControllerSourceTypePhotoLibrary;
    UIImagePickerController *imagePickerController = [[ImagePicker alloc] init];
    if (NO && [[UIDevice currentDevice] userInterfaceIdiom] == UIUserInterfaceIdiomPad) {
        popover = [[UIPopoverController alloc] initWithContentViewController:imagePickerController];
        [popover presentPopoverFromBarButtonItem:g_sender permittedArrowDirections:UIPopoverArrowDirectionAny animated:YES];
    } else {
        [self presentModalViewController:imagePickerController animated:YES];
    }
}
- (IBAction)cbfr_loadPic:(id)sender
{
    g_sender = sender;
	UIActionSheet *menu = [[UIActionSheet alloc]
						   initWithTitle: nil
						   delegate:self
						   cancelButtonTitle:@"Cancel"
						   destructiveButtonTitle:nil
						   otherButtonTitles:@"Take Photo", @"Choose Existing", nil];
	menu.tag = 1;
    menu.actionSheetStyle = UIActionSheetStyleBlackOpaque;
	[menu showInView:self.view];
}
- (IBAction)cbfr_loadPic2
{
	UIActionSheet *menu = [[UIActionSheet alloc] 
						   initWithTitle: nil 
						   delegate:self
						   cancelButtonTitle:nil
						   destructiveButtonTitle:nil
						   otherButtonTitles:@"Take Photo", @"Choose Existing", @"Cancel", nil];
	menu.tag = 1;
	[menu showInView:self.view];
}
- (id)initWithNibName:(NSString *)nibNameOrNil bundle:(NSBundle *)nibBundleOrNil {
    if (self = [super initWithNibName:nibNameOrNil bundle:nibBundleOrNil]) {
		g_mvc = self;
    }
    return self;
}
- (void)cbfr_showImage:(CGImageRef)img {   
	g_iCanSave = 1;
	static CGImageRef _show;
	CGImageRef image0 = CGImageCreateCopy(img);
	((UIImageView *)([self.view viewWithTag:100])).image = [UIImage imageWithCGImage:image0];
	if (_show)
	{
		CGImageRelease(_show);
		_show = nil;
	}
	_show = image0;
}
- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
}
- (void)adViewDidReceiveAd:(GADBannerView *)bannerView
{
    NSLog(@"adViewDidReceiveAd");
}
- (void)bannerViewDidReceiveAd:(nonnull GADBannerView *)bannerView
{
#ifdef CBFR_KW_DEBUG
    NSLog(@"%s", __PRETTY_FUNCTION__);
#endif
}
- (void)bannerView:(nonnull GADBannerView *)bannerView didFailToReceiveAdWithError:(nonnull NSError *)error
{
}
- (void)bannerViewDidRecordImpression:(nonnull GADBannerView *)bannerView
{
}
- (void)bannerViewDidRecordClick:(nonnull GADBannerView *)bannerView
{
}
- (void)bannerViewWillPresentScreen:(nonnull GADBannerView *)bannerView
{
}
- (void)bannerViewWillDismissScreen:(nonnull GADBannerView *)bannerView
{
}
- (void)bannerViewDidDismissScreen:(nonnull GADBannerView *)bannerView
{
}
- (void)cbfr_showFeatureGuideView
{
    UINavigationItem *item0 = _nav_bar.topItem;
    UIBarButtonItem *item3 = item0.leftBarButtonItem;
    UIView *targetView3 = (UIView *)[item3 performSelector:@selector(view)];
    UIBarButtonItem *item4 = item0.rightBarButtonItem;
    UIView *targetView4 = (UIView *)[item4 performSelector:@selector(view)];
    UIView *targetView5 = [self.view viewWithTag:1001];
    UIView *targetView6 = [self.view viewWithTag:1002];
    if (1)
    {
        CBFREAFeatureItem *item8 = nil;
        if (targetView3)
        {
            item8 = [[CBFREAFeatureItem alloc] initWithFocusView:targetView3 cbfr_focusCornerRadius:0 cbfr_focusInsets:UIEdgeInsetsMake(5, -10, 5, 10)];
            item8.cbfr_introduce = @"Tap here to start";
        }
        CBFREAFeatureItem *item9 = nil;
        if (targetView4)
        {
            item9 = [[CBFREAFeatureItem alloc] initWithFocusView:targetView4 cbfr_focusCornerRadius:0 cbfr_focusInsets:UIEdgeInsetsMake(5, -10, 5, 10)];
            item9.cbfr_introduce = @"More features";
        }
        CBFREAFeatureItem *item10 = nil;
        if (targetView5)
        {
            item10 = [[CBFREAFeatureItem alloc] initWithFocusView:targetView5 cbfr_focusCornerRadius:0 cbfr_focusInsets:UIEdgeInsetsMake(5, -10, 5, 10)];
            item10.cbfr_introduce = @"Change filter";
        }
        CBFREAFeatureItem *item11 = nil;
        if (targetView6)
        {
            item11 = [[CBFREAFeatureItem alloc] initWithFocusView:targetView6 cbfr_focusCornerRadius:0 cbfr_focusInsets:UIEdgeInsetsMake(5, -10, 5, 10)];
            item11.cbfr_introduce = @"Change filter";
        }
        NSBundle * bundle1 = [NSBundle mainBundle];
        NSString *saveKeyName = [NSString stringWithFormat:@"cbfr_showFeatureGuideView-%@", [bundle1 objectForInfoDictionaryKey: @"CFBundleShortVersionString"]];
        NSString *version01 = [bundle1 objectForInfoDictionaryKey: @"CFBundleShortVersionString"];
#ifdef CBFR_KW_DEBUG
        NSLog(@"saveKeyName = %@", saveKeyName);
#endif
        BOOL hasShow = [UIView cbfr_hasShowFeatureGuideWithKey:saveKeyName version:version01];
        if (hasShow)
        {
            [self cbfr_showUMP];
        }
        [self.view cbfr_showWithFeatureItems:@[ item8, item9, item10, item11] saveKeyName:saveKeyName inVersion:version01];
    }
}
- (void)viewDidAppear:(BOOL)animated     
{
    [super viewDidAppear:animated];
#ifdef CBFR_KW_DEBUG
{
    int scm = ((CBFRbwCameraAppDelegate*)([UIApplication sharedApplication].delegate)).cbfr_in_screen_capture_mode;
    if (scm == 1)
    {
        [[[ImagePicker alloc] init] cbfr_processImage:[UIImage imageNamed:@"cbfr_IMG_0001.JPG"]];
        return;
    }
}
#endif
    [self cbfr_showFeatureGuideView];
}
- (void)cbfr_loadForm {
  [UMPConsentForm loadWithCompletionHandler:^(UMPConsentForm *form,
                                              NSError *loadError) {
    if (loadError) {
    } else {
#ifdef CBFR_KW_ADMOB_TEST
        NSLog(@"UMPConsentInformation.sharedInstance.consentStatus = %d", UMPConsentInformation.sharedInstance.consentStatus);
#endif
      if (UMPConsentInformation.sharedInstance.consentStatus ==
          UMPConsentStatusRequired) {
        [form
            presentFromViewController:self
                    completionHandler:^(NSError *_Nullable dismissError) {
                      if (UMPConsentInformation.sharedInstance.consentStatus ==
                          UMPConsentStatusObtained) {
                      }
                        [self cbfr_loadForm];
                    }];
      } else {
      }
    }
  }];
}
-(void)cbfr_showUMP
{
    static BOOL showed = NO;
    if (showed)
        return;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        showed = YES;
    });
    if (true)
    {
        UMPRequestParameters *parameters = [[UMPRequestParameters alloc] init];
        parameters.tagForUnderAgeOfConsent = NO;
        UMPDebugSettings *debugSettings = [[UMPDebugSettings alloc] init];
#ifdef CBFR_KW_ADMOB_TEST
        debugSettings.testDeviceIdentifiers = @[ @"D28AE46F-8A80-41BE-89FA-449B9856AED5" ];
        debugSettings.geography = UMPDebugGeographyEEA;
#endif
        parameters.debugSettings = debugSettings;
        [UMPConsentInformation.sharedInstance
         requestConsentInfoUpdateWithParameters:parameters
         completionHandler:^(NSError* _Nullable error) {
            if (error) {
            } else {
                UMPFormStatus formStatus =
                UMPConsentInformation.sharedInstance
                    .formStatus;
#ifdef CBFR_KW_ADMOB_TEST
                NSLog(@"formStatus = %d", formStatus);
#endif
                if (formStatus == UMPFormStatusAvailable) {
                    [self cbfr_loadForm];
                }
            }
        }];
    }
}
- (void)viewDidLoad
{
	_nav_bar.topItem.title = [NSString stringWithCString:_filter[_current_filter]];
#if ENABLE_ADMOB
#ifdef CBFR_KW_DEBUG
{
    int scm = ((CBFRbwCameraAppDelegate*)([UIApplication sharedApplication].delegate)).cbfr_in_screen_capture_mode;
    if (scm == 1)
    {
        return;
    }
}
#endif
    {
        CGRect appBound = [[UIScreen mainScreen]applicationFrame];
        bannerView_ = [[GADBannerView alloc]
                       initWithFrame:CGRectMake((appBound.size.width - GADAdSizeBanner.size.width) / 2,
                                                5,
                                                GADAdSizeBanner.size.width,
                                                GADAdSizeBanner.size.height)];
        bannerView_.autoresizingMask  = (UIViewAutoresizingFlexibleLeftMargin | UIViewAutoresizingFlexibleRightMargin | UIViewAutoresizingFlexibleTopMargin | UIViewAutoresizingFlexibleBottomMargin);
        NSString *ad_unit_id = nil; 
#ifdef CBFR_KW_DEBUG
        NSLog(@"config ad id = %@", ad_unit_id);
#endif
        if (ad_unit_id == nil)
            ad_unit_id = APP_BANNER_UNIT_ID;
        bannerView_.adUnitID = ad_unit_id;
#ifdef CBFR_KW_DEBUG
#endif
        bannerView_.rootViewController = self;
        [bannerView_ setDelegate:self];
        [adParent addSubview:bannerView_];
        GADRequest * request= [GADRequest request];
        [bannerView_ loadRequest: request];
        bannerView_.autoresizingMask  = (UIViewAutoresizingFlexibleLeftMargin | UIViewAutoresizingFlexibleRightMargin | UIViewAutoresizingFlexibleTopMargin | UIViewAutoresizingFlexibleBottomMargin);
    }
#endif
}
#ifdef CBFR_KW_DEBUG
#define SAFE_AREA_LOG(insets) NSLog(@ "%s T:%.1f R:%.1f B:%.1f L:%.1f", __FUNCTION__, insets.top, insets.right, insets.bottom, insets. left)
#else
#define SAFE_AREA_LOG(insets)
#endif
- ( void ) viewWillLayoutSubviews {
    [ super viewWillLayoutSubviews ];
    CGRect adjustedRect = self.view2.frame;
    if (@available(ios 11.0, *))
    {
        SAFE_AREA_LOG(self.view.safeAreaInsets);
    }
    if (@available(ios 11.0, *))
    {
        adjustedRect = UIEdgeInsetsInsetRect(self.view.bounds, self.view.safeAreaInsets);
#ifdef CBFR_KW_DEBUG
        NSLog(@"adjustedRect = %@", NSStringFromCGRect(adjustedRect));
#endif
    }
    else if (@available(ios 9.0, *))
    {
        CGFloat f1 = self.topLayoutGuide.length;
        CGFloat f2 = self.bottomLayoutGuide.length;
        UIEdgeInsets ei = UIEdgeInsetsMake(f1, 0, f2, 0);
        adjustedRect = UIEdgeInsetsInsetRect(self.view.bounds, ei);
#ifdef CBFR_KW_DEBUG
        NSLog(@"adjustedRect = %@", NSStringFromCGRect(adjustedRect));
#endif
    }
    else if (@available(ios 7.0, *))
    {
        CGFloat f1 = 20;
        CGFloat f2 = 0;
        UIEdgeInsets ei = UIEdgeInsetsMake(f1, 0, f2, 0);
        adjustedRect = UIEdgeInsetsInsetRect(self.view.bounds, ei);
#ifdef CBFR_KW_DEBUG
        NSLog(@"adjustedRect = %@", NSStringFromCGRect(adjustedRect));
#endif
    }
    self.view2.frame = adjustedRect;
    GADAdSize gs0 = bannerView_.adSize;
    GADAdSize gs1 = GADCurrentOrientationAnchoredAdaptiveBannerAdSizeWithWidth(adjustedRect.size.width);
    CGSize s0 = CGSizeFromGADAdSize(gs0);
    CGSize s1 = CGSizeFromGADAdSize(gs1);
    if (!GADAdSizeEqualToSize(gs0, gs1))
    {
#ifdef CBFR_KW_DEBUG
        NSLog(@"gs0 = %@", NSStringFromGADAdSize(gs0));
        NSLog(@"gs1 = %@", NSStringFromGADAdSize(gs1));
        NSLog(@"s0 = %@", NSStringFromCGSize(s0));
        NSLog(@"s1 = %@", NSStringFromCGSize(s1));
#endif
        UIImageView * iv1 = [self.view viewWithTag:100];
        CGRect r0 = adParent.frame;
        CGRect r1 = iv1.frame;
        CGFloat h1 = r0.size.height + r1.size.height;
        bannerView_.adSize = gs1;
        adParent.frame = CGRectMake(0, 0, adjustedRect.size.width, s1.height + 10);
        bannerView_.frame = CGRectMake(0, 5, s1.width, s1.height);;
        iv1.frame = CGRectMake(0, gs1.size.height + 10, r1.size.width, h1 - (gs1.size.height + 10));
    }
}
- ( void ) viewDidLayoutSubviews {
    [ super viewDidLayoutSubviews ];
    if (@available(ios 11.0, *))
    {
        SAFE_AREA_LOG(self.view.safeAreaInsets);
    }
}
- (void)viewDidUnload {
}
- (void)dealloc {
	[super dealloc];
}

@end
