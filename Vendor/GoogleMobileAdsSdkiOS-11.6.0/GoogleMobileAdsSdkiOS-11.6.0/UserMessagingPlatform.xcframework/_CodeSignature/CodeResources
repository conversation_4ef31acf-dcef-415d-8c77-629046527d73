<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>ios-arm64/UserMessagingPlatform.framework/Headers/UMPConsentForm.h</key>
		<data>
		z6mtCk+9rFiXmTUvx1iwu7OD7Yc=
		</data>
		<key>ios-arm64/UserMessagingPlatform.framework/Headers/UMPConsentInformation.h</key>
		<data>
		nTwROE/A7JrgHOlBRVyp2eJrGZU=
		</data>
		<key>ios-arm64/UserMessagingPlatform.framework/Headers/UMPDebugSettings.h</key>
		<data>
		sTZSuUtSAmUeGMutCHiZgEGxAbk=
		</data>
		<key>ios-arm64/UserMessagingPlatform.framework/Headers/UMPError.h</key>
		<data>
		dap5fPtno9hcuFhzft3VWvMy4f0=
		</data>
		<key>ios-arm64/UserMessagingPlatform.framework/Headers/UMPRequestParameters.h</key>
		<data>
		biWyekLSQxSKsxHTjJ8KDsmj7qQ=
		</data>
		<key>ios-arm64/UserMessagingPlatform.framework/Headers/UserMessagingPlatform.h</key>
		<data>
		9RCFJSaqhLHeEgqG9d/eph7W3lI=
		</data>
		<key>ios-arm64/UserMessagingPlatform.framework/Info.plist</key>
		<data>
		oLNFcROXyO9JBbeiocAMi/IDLf0=
		</data>
		<key>ios-arm64/UserMessagingPlatform.framework/Modules/module.modulemap</key>
		<data>
		RkG2jbWyWyEbQ8hlPX98EMJhvZc=
		</data>
		<key>ios-arm64/UserMessagingPlatform.framework/PrivacyInfo.xcprivacy</key>
		<data>
		k9OFlDPBRWfteJ+bfG12WAoBuls=
		</data>
		<key>ios-arm64/UserMessagingPlatform.framework/UserMessagingPlatform</key>
		<data>
		7XNbAgrl3xZ0bhc5P5BUVDhTJb0=
		</data>
		<key>ios-arm64_x86_64-simulator/UserMessagingPlatform.framework/Headers/UMPConsentForm.h</key>
		<data>
		z6mtCk+9rFiXmTUvx1iwu7OD7Yc=
		</data>
		<key>ios-arm64_x86_64-simulator/UserMessagingPlatform.framework/Headers/UMPConsentInformation.h</key>
		<data>
		nTwROE/A7JrgHOlBRVyp2eJrGZU=
		</data>
		<key>ios-arm64_x86_64-simulator/UserMessagingPlatform.framework/Headers/UMPDebugSettings.h</key>
		<data>
		sTZSuUtSAmUeGMutCHiZgEGxAbk=
		</data>
		<key>ios-arm64_x86_64-simulator/UserMessagingPlatform.framework/Headers/UMPError.h</key>
		<data>
		dap5fPtno9hcuFhzft3VWvMy4f0=
		</data>
		<key>ios-arm64_x86_64-simulator/UserMessagingPlatform.framework/Headers/UMPRequestParameters.h</key>
		<data>
		biWyekLSQxSKsxHTjJ8KDsmj7qQ=
		</data>
		<key>ios-arm64_x86_64-simulator/UserMessagingPlatform.framework/Headers/UserMessagingPlatform.h</key>
		<data>
		9RCFJSaqhLHeEgqG9d/eph7W3lI=
		</data>
		<key>ios-arm64_x86_64-simulator/UserMessagingPlatform.framework/Info.plist</key>
		<data>
		1DaZ59CQBNZM+DQ47Yvug7jDwQg=
		</data>
		<key>ios-arm64_x86_64-simulator/UserMessagingPlatform.framework/Modules/module.modulemap</key>
		<data>
		RkG2jbWyWyEbQ8hlPX98EMJhvZc=
		</data>
		<key>ios-arm64_x86_64-simulator/UserMessagingPlatform.framework/PrivacyInfo.xcprivacy</key>
		<data>
		k9OFlDPBRWfteJ+bfG12WAoBuls=
		</data>
		<key>ios-arm64_x86_64-simulator/UserMessagingPlatform.framework/UserMessagingPlatform</key>
		<data>
		JaULajHZvCCewDkohAhcRXcLfa8=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>ios-arm64/UserMessagingPlatform.framework/Headers/UMPConsentForm.h</key>
		<dict>
			<key>hash</key>
			<data>
			z6mtCk+9rFiXmTUvx1iwu7OD7Yc=
			</data>
			<key>hash2</key>
			<data>
			OwZyBXD77uoq+ek46dD7dqPf76vxTPXCg9wmmmzG2dc=
			</data>
		</dict>
		<key>ios-arm64/UserMessagingPlatform.framework/Headers/UMPConsentInformation.h</key>
		<dict>
			<key>hash</key>
			<data>
			nTwROE/A7JrgHOlBRVyp2eJrGZU=
			</data>
			<key>hash2</key>
			<data>
			ofTAHdcbyTaK9Dd3vFWZfDh6sx/22bavVkEUQrKx8kk=
			</data>
		</dict>
		<key>ios-arm64/UserMessagingPlatform.framework/Headers/UMPDebugSettings.h</key>
		<dict>
			<key>hash</key>
			<data>
			sTZSuUtSAmUeGMutCHiZgEGxAbk=
			</data>
			<key>hash2</key>
			<data>
			jW8LeaN56pcVFeX4YU8KyHfdeQa6ubqv2Cc/HWP7cbU=
			</data>
		</dict>
		<key>ios-arm64/UserMessagingPlatform.framework/Headers/UMPError.h</key>
		<dict>
			<key>hash</key>
			<data>
			dap5fPtno9hcuFhzft3VWvMy4f0=
			</data>
			<key>hash2</key>
			<data>
			v0umJGzT5bRrYkwGJzPCZ9JC74kxjka7RH9xyLveHpg=
			</data>
		</dict>
		<key>ios-arm64/UserMessagingPlatform.framework/Headers/UMPRequestParameters.h</key>
		<dict>
			<key>hash</key>
			<data>
			biWyekLSQxSKsxHTjJ8KDsmj7qQ=
			</data>
			<key>hash2</key>
			<data>
			I/ZkAH7XX1RbIA+P/tfB/7MPDRl2RorB5sh/YbPq/5Y=
			</data>
		</dict>
		<key>ios-arm64/UserMessagingPlatform.framework/Headers/UserMessagingPlatform.h</key>
		<dict>
			<key>hash</key>
			<data>
			9RCFJSaqhLHeEgqG9d/eph7W3lI=
			</data>
			<key>hash2</key>
			<data>
			oIfOo52szNDhoW7/k2wegqpLbm/gh4ehYlgE9ik6M2U=
			</data>
		</dict>
		<key>ios-arm64/UserMessagingPlatform.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			oLNFcROXyO9JBbeiocAMi/IDLf0=
			</data>
			<key>hash2</key>
			<data>
			Bs/ZPWVQprOaplfMn5Ce7CiwlEbGYQoGsgjRJuVqZ58=
			</data>
		</dict>
		<key>ios-arm64/UserMessagingPlatform.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			RkG2jbWyWyEbQ8hlPX98EMJhvZc=
			</data>
			<key>hash2</key>
			<data>
			58hJ+pDAaGDwJPOU6I9Te4Vv2O/oXyVD+XQQyTwdk94=
			</data>
		</dict>
		<key>ios-arm64/UserMessagingPlatform.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			k9OFlDPBRWfteJ+bfG12WAoBuls=
			</data>
			<key>hash2</key>
			<data>
			RbetuZ/NLZYqHIAPv8wTJbV6f6QWX0FhgjI7tf/pyQA=
			</data>
		</dict>
		<key>ios-arm64/UserMessagingPlatform.framework/UserMessagingPlatform</key>
		<dict>
			<key>hash</key>
			<data>
			7XNbAgrl3xZ0bhc5P5BUVDhTJb0=
			</data>
			<key>hash2</key>
			<data>
			8GVz38eoZdyaagrSh9HXGHpbWCAt9/CXbBxNWMpipS0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/UserMessagingPlatform.framework/Headers/UMPConsentForm.h</key>
		<dict>
			<key>hash</key>
			<data>
			z6mtCk+9rFiXmTUvx1iwu7OD7Yc=
			</data>
			<key>hash2</key>
			<data>
			OwZyBXD77uoq+ek46dD7dqPf76vxTPXCg9wmmmzG2dc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/UserMessagingPlatform.framework/Headers/UMPConsentInformation.h</key>
		<dict>
			<key>hash</key>
			<data>
			nTwROE/A7JrgHOlBRVyp2eJrGZU=
			</data>
			<key>hash2</key>
			<data>
			ofTAHdcbyTaK9Dd3vFWZfDh6sx/22bavVkEUQrKx8kk=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/UserMessagingPlatform.framework/Headers/UMPDebugSettings.h</key>
		<dict>
			<key>hash</key>
			<data>
			sTZSuUtSAmUeGMutCHiZgEGxAbk=
			</data>
			<key>hash2</key>
			<data>
			jW8LeaN56pcVFeX4YU8KyHfdeQa6ubqv2Cc/HWP7cbU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/UserMessagingPlatform.framework/Headers/UMPError.h</key>
		<dict>
			<key>hash</key>
			<data>
			dap5fPtno9hcuFhzft3VWvMy4f0=
			</data>
			<key>hash2</key>
			<data>
			v0umJGzT5bRrYkwGJzPCZ9JC74kxjka7RH9xyLveHpg=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/UserMessagingPlatform.framework/Headers/UMPRequestParameters.h</key>
		<dict>
			<key>hash</key>
			<data>
			biWyekLSQxSKsxHTjJ8KDsmj7qQ=
			</data>
			<key>hash2</key>
			<data>
			I/ZkAH7XX1RbIA+P/tfB/7MPDRl2RorB5sh/YbPq/5Y=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/UserMessagingPlatform.framework/Headers/UserMessagingPlatform.h</key>
		<dict>
			<key>hash</key>
			<data>
			9RCFJSaqhLHeEgqG9d/eph7W3lI=
			</data>
			<key>hash2</key>
			<data>
			oIfOo52szNDhoW7/k2wegqpLbm/gh4ehYlgE9ik6M2U=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/UserMessagingPlatform.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			1DaZ59CQBNZM+DQ47Yvug7jDwQg=
			</data>
			<key>hash2</key>
			<data>
			uvk7pNdsJ5y/YPFIjO4vhWe9wzmNv9USBepEzd4wVs0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/UserMessagingPlatform.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			RkG2jbWyWyEbQ8hlPX98EMJhvZc=
			</data>
			<key>hash2</key>
			<data>
			58hJ+pDAaGDwJPOU6I9Te4Vv2O/oXyVD+XQQyTwdk94=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/UserMessagingPlatform.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			k9OFlDPBRWfteJ+bfG12WAoBuls=
			</data>
			<key>hash2</key>
			<data>
			RbetuZ/NLZYqHIAPv8wTJbV6f6QWX0FhgjI7tf/pyQA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/UserMessagingPlatform.framework/UserMessagingPlatform</key>
		<dict>
			<key>hash</key>
			<data>
			JaULajHZvCCewDkohAhcRXcLfa8=
			</data>
			<key>hash2</key>
			<data>
			pcnV1E8nDx3ENCzsG3ofvCY3haGa+ZSfYkLR8Zzf9OE=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
