framework module GoogleMobileAds {
  umbrella header "GoogleMobileAds.h"

  export *
  module * { export * }

  link "sqlite3"
  link "z"

  link framework "AdSupport"
  link framework "AudioToolbox"
  link framework "AVFoundation"
  link framework "CFNetwork"
  link framework "CoreGraphics"
  link framework "CoreMedia"
  link framework "CoreTelephony"
  link framework "CoreVideo"
  link framework "Foundation"
  link framework "JavaScriptCore"
  link framework "MediaPlayer"
  link framework "MessageUI"
  link framework "MobileCoreServices"
  link framework "QuartzCore"
  link framework "SafariServices"
  link framework "Security"
  link framework "StoreKit"
  link framework "SystemConfiguration"
  link framework "UIKit"
  link framework "WebKit"

  header "GADAdChoicesView.h"
  header "GADAdFormat.h"
  header "GADAdLoader.h"
  header "GADAdLoaderAdTypes.h"
  header "GADAdLoaderDelegate.h"
  header "GADAdMetadata.h"
  header "GADAdNetworkExtras.h"
  header "GADAdReward.h"
  header "GADAdSize.h"
  header "GADAdSizeDelegate.h"
  header "GADAdValue.h"
  header "GADAppEventDelegate.h"
  header "GADAppOpenAd.h"
  header "GADAudioVideoManager.h"
  header "GADAudioVideoManagerDelegate.h"
  header "GADBannerView.h"
  header "GADBannerViewDelegate.h"
  header "GADCustomEventBanner.h"
  header "GADCustomEventBannerDelegate.h"
  header "GADCustomEventExtras.h"
  header "GADCustomEventInterstitial.h"
  header "GADCustomEventInterstitialDelegate.h"
  header "GADCustomEventNativeAd.h"
  header "GADCustomEventNativeAdDelegate.h"
  header "GADCustomEventParameters.h"
  header "GADCustomEventRequest.h"
  header "GADCustomNativeAd.h"
  header "GADCustomNativeAdDelegate.h"
  header "GADDebugOptionsViewController.h"
  header "GADDisplayAdMeasurement.h"
  header "GADDynamicHeightSearchRequest.h"
  header "GADExtras.h"
  header "GADFullScreenContentDelegate.h"
  header "GADInitializationStatus.h"
  header "GADInterstitialAd.h"
  header "GADMediaAspectRatio.h"
  header "GADMediaContent.h"
  header "GADMediaView.h"
  header "GADMobileAds.h"
  header "GADMultipleAdsAdLoaderOptions.h"
  header "GADMuteThisAdReason.h"
  header "GADNativeAd+ConfirmationClick.h"
  header "GADNativeAd+CustomClickGesture.h"
  header "GADNativeAd.h"
  header "GADNativeAdAssetIdentifiers.h"
  header "GADNativeAdDelegate.h"
  header "GADNativeAdImage+Mediation.h"
  header "GADNativeAdImage.h"
  header "GADNativeAdImageAdLoaderOptions.h"
  header "GADNativeAdMediaAdLoaderOptions.h"
  header "GADNativeAdUnconfirmedClickDelegate.h"
  header "GADNativeAdViewAdOptions.h"
  header "GADNativeMuteThisAdLoaderOptions.h"
  header "GADPresentationError.h"
  header "GADRequest.h"
  header "GADRequestConfiguration.h"
  header "GADRequestError.h"
  header "GADResponseInfo.h"
  header "GADRewardedAd.h"
  header "GADRewardedInterstitialAd.h"
  header "GADSearchBannerView.h"
  header "GADServerSideVerificationOptions.h"
  header "GADVideoController.h"
  header "GADVideoControllerDelegate.h"
  header "GADVideoOptions.h"
  header "GAMBannerView.h"
  header "GAMBannerViewOptions.h"
  header "GAMInterstitialAd.h"
  header "GAMRequest.h"
  header "GoogleMobileAdsDefines.h"
  header "Mediation/GADMAdNetworkAdapterProtocol.h"
  header "Mediation/GADMAdNetworkConnectorProtocol.h"
  header "Mediation/GADMEnums.h"
  header "Mediation/GADMRewardBasedVideoAdNetworkAdapterProtocol.h"
  header "Mediation/GADMRewardBasedVideoAdNetworkConnectorProtocol.h"
  header "Mediation/GADMediatedUnifiedNativeAd.h"
  header "Mediation/GADMediatedUnifiedNativeAdNotificationSource.h"
  header "Mediation/GADMediationAd.h"
  header "Mediation/GADMediationAdConfiguration.h"
  header "Mediation/GADMediationAdEventDelegate.h"
  header "Mediation/GADMediationAdRequest.h"
  header "Mediation/GADMediationAdSize.h"
  header "Mediation/GADMediationAdapter.h"
  header "Mediation/GADMediationBannerAd.h"
  header "Mediation/GADMediationInterstitialAd.h"
  header "Mediation/GADMediationNativeAd.h"
  header "Mediation/GADMediationRewardedAd.h"
  header "Mediation/GADMediationServerConfiguration.h"
  header "Mediation/GADVersionNumber.h"
  header "RTBMediation/GADRTBAdapter.h"
  header "RTBMediation/GADRTBRequestParameters.h"
}
