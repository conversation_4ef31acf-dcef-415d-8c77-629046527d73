@class CBFRMainViewController;
@interface CBFRbwCameraAppDelegate : NSObject <UIApplicationDelegate> {
    UIWindow *window;
    CBFRMainViewController *cbfr_mainViewController;
}
@property (nonatomic, retain) IBOutlet UIWindow *window;
@property (nonatomic, retain) CBFRMainViewController *cbfr_mainViewController;
#ifdef CBFR_KW_DEBUG
@property(nonatomic,assign) int cbfr_in_screen_capture_mode;
#endif
@end
