framework module GoogleMobileAds {
  umbrella header "GoogleMobileAds.h"

  export *
  module * { export * }

  link framework "AdSupport"
  link framework "AudioToolbox"
  link framework "AVFoundation"
  link framework "CoreGraphics"
  link framework "CoreMedia"
  link framework "CoreMotion"
  link framework "CoreTelephony"
  link framework "CoreVideo"
  link framework "Foundation"
  link framework "GLKit"
  link framework "MediaPlayer"
  link framework "MessageUI"
  link framework "MobileCoreServices"
  link framework "OpenGLES"
  link framework "SafariServices"
  link framework "StoreKit"
  link framework "SystemConfiguration"
  link framework "UIKit"
  link framework "WebKit"

  header "GoogleMobileAdsDefines.h"

  header "GADAdDelegate.h"
  header "GADAdNetworkExtras.h"
  header "GADAdSize.h"
  header "GADBannerView.h"
  header "GADBannerViewDelegate.h"
  header "GADCorrelator.h"
  header "GADCorrelatorAdLoaderOptions.h"
  header "GADDebugOptionsViewController.h"
  header "GADExtras.h"
  header "GADInAppPurchase.h"
  header "GADInAppPurchaseDelegate.h"
  header "GADInterstitial.h"
  header "GADInterstitialDelegate.h"
  header "GADMediaView.h"
  header "GADMobileAds.h"
  header "GADNativeExpressAdView.h"
  header "GADNativeExpressAdViewDelegate.h"
  header "GADRequest.h"
  header "GADRequestError.h"
  header "GADVideoController.h"
  header "GADVideoControllerDelegate.h"
  header "GADVideoOptions.h"

  header "DFPBannerView.h"
  header "DFPCustomRenderedAd.h"
  header "DFPCustomRenderedBannerViewDelegate.h"
  header "DFPCustomRenderedInterstitialDelegate.h"
  header "DFPInterstitial.h"
  header "DFPRequest.h"
  header "GADAdSizeDelegate.h"
  header "GADAppEventDelegate.h"

  header "GADAdLoader.h"
  header "GADAdLoaderAdTypes.h"
  header "GADAdLoaderDelegate.h"

  header "GADNativeAd.h"
  header "GADNativeAdDelegate.h"
  header "GADNativeAdImage.h"
  header "GADNativeAdImage+Mediation.h"
  header "GADNativeAppInstallAd.h"
  header "GADNativeContentAd.h"
  header "GADNativeCustomTemplateAd.h"

  header "GADNativeAdImageAdLoaderOptions.h"
  header "GADNativeAdViewAdOptions.h"

  header "GADCustomEventBanner.h"
  header "GADCustomEventBannerDelegate.h"
  header "GADCustomEventExtras.h"
  header "GADCustomEventInterstitial.h"
  header "GADCustomEventInterstitialDelegate.h"
  header "GADCustomEventNativeAd.h"
  header "GADCustomEventNativeAdDelegate.h"
  header "GADCustomEventParameters.h"
  header "GADCustomEventRequest.h"
  header "GADMediatedNativeAd.h"
  header "GADMediatedNativeAdDelegate.h"
  header "GADMediatedNativeAdNotificationSource.h"
  header "GADMediatedNativeAppInstallAd.h"
  header "GADMediatedNativeContentAd.h"

  header "GADDynamicHeightSearchRequest.h"
  header "GADSearchBannerView.h"
  header "GADSearchRequest.h"

  header "GADAdReward.h"
  header "GADRewardBasedVideoAd.h"
  header "GADRewardBasedVideoAdDelegate.h"
}
