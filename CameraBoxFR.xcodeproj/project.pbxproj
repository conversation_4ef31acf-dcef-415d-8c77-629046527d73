// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 60;
	objects = {

/* Begin PBXBuildFile section */
		0A811C911DAB32280045F266 /* cbfr_Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 0A811C901DAB32280045F266 /* cbfr_Images.xcassets */; };
		0A811C9F1DAB34A80045F266 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 0A811C9D1DAB34A80045F266 /* LaunchScreen.storyboard */; };
		0A811D3B1DAB4B280045F266 /* libxml2.2.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 0A811D3A1DAB4B280045F266 /* libxml2.2.tbd */; };
		0A811D3D1DAB4B490045F266 /* MessageUI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0A811D3C1DAB4B490045F266 /* MessageUI.framework */; };
		0ABCF70B1DACB85900EFF386 /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0ABCF70A1DACB85900EFF386 /* AudioToolbox.framework */; };
		0ABCF70D1DACB86D00EFF386 /* AVFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0ABCF70C1DACB86D00EFF386 /* AVFoundation.framework */; };
		0ABCF70F1DACB88000EFF386 /* CoreTelephony.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0ABCF70E1DACB88000EFF386 /* CoreTelephony.framework */; };
		0ABCF7131DACBA4E00EFF386 /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0ABCF7121DACBA4E00EFF386 /* CoreAudio.framework */; };
		0ABCF7151DACBA7E00EFF386 /* CoreMedia.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0ABCF7141DACBA7D00EFF386 /* CoreMedia.framework */; };
		0ABCF7171DACBA8F00EFF386 /* MediaPlayer.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0ABCF7161DACBA8F00EFF386 /* MediaPlayer.framework */; };
		0ABCF7191DACBB0100EFF386 /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0ABCF7181DACBB0100EFF386 /* QuartzCore.framework */; };
		0ABCF71B1DACBB4F00EFF386 /* SystemConfiguration.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0ABCF71A1DACBB4F00EFF386 /* SystemConfiguration.framework */; };
		0ABCF71D1DACBB6B00EFF386 /* AdSupport.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0ABCF71C1DACBB6B00EFF386 /* AdSupport.framework */; };
		0ABCF71F1DACBBAA00EFF386 /* EventKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0ABCF71E1DACBBAA00EFF386 /* EventKit.framework */; };
		0ABCF7211DACBBB000EFF386 /* EventKitUI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0ABCF7201DACBBB000EFF386 /* EventKitUI.framework */; };
		0ABCF7231DACCDF400EFF386 /* OpenGLES.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0ABCF7221DACCDF400EFF386 /* OpenGLES.framework */; };
		0ABCF7251DACCE3600EFF386 /* SafariServices.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0ABCF7241DACCE3600EFF386 /* SafariServices.framework */; };
		0ABCF7271DACCE4D00EFF386 /* CoreMotion.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0ABCF7261DACCE4D00EFF386 /* CoreMotion.framework */; };
		0ABCF7291DACCE8A00EFF386 /* GLKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0ABCF7281DACCE8A00EFF386 /* GLKit.framework */; };
		0ABCF72B1DACCEB000EFF386 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0ABCF72A1DACCEB000EFF386 /* CoreVideo.framework */; };
		0ABCF72D1DACCED800EFF386 /* MobileCoreServices.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0ABCF72C1DACCED800EFF386 /* MobileCoreServices.framework */; };
		1D3623260D0F684500981E51 /* CBFRbwCameraAppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 1D3623250D0F684500981E51 /* CBFRbwCameraAppDelegate.m */; settings = {COMPILER_FLAGS = "-fobjc-arc"; }; };
		1D60589B0D05DD56006BFB54 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 29B97316FDCFA39411CA2CEA /* main.m */; settings = {COMPILER_FLAGS = "-fobjc-arc"; }; };
		1D60589F0D05DD5A006BFB54 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1D30AB110D05D00D00671497 /* Foundation.framework */; };
		1DF5F4E00D08C38300B7A737 /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1DF5F4DF0D08C38300B7A737 /* UIKit.framework */; };
		280E754E0DD40C5E005A515E /* CBFRMainView.xib in Resources */ = {isa = PBXBuildFile; fileRef = 280E754B0DD40C5E005A515E /* CBFRMainView.xib */; };
		280E754F0DD40C5E005A515E /* MainWindow.xib in Resources */ = {isa = PBXBuildFile; fileRef = 280E754C0DD40C5E005A515E /* MainWindow.xib */; };
		288765590DF743DE002DB57D /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 288765580DF743DE002DB57D /* CoreGraphics.framework */; };
		289233A70DB2D0840083E9F9 /* CBFRMainView.m in Sources */ = {isa = PBXBuildFile; fileRef = 289233A50DB2D0840083E9F9 /* CBFRMainView.m */; settings = {COMPILER_FLAGS = "-fobjc-arc"; }; };
		289233AE0DB2D0DB0083E9F9 /* CBFRMainViewController.mm in Sources */ = {isa = PBXBuildFile; fileRef = 289233A90DB2D0DB0083E9F9 /* CBFRMainViewController.mm */; settings = {COMPILER_FLAGS = "-fobjc-arc"; }; };
		483B20632C20F28000B24FE6 /* GoogleMobileAdsPlaceholder.swift in Sources */ = {isa = PBXBuildFile; fileRef = 483B20602C20F28000B24FE6 /* GoogleMobileAdsPlaceholder.swift */; };
		483B20652C20F2B100B24FE6 /* GoogleMobileAds.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 483B20612C20F28000B24FE6 /* GoogleMobileAds.xcframework */; };
		483B20682C20F2C700B24FE6 /* UserMessagingPlatform.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 483B205F2C20F28000B24FE6 /* UserMessagingPlatform.xcframework */; };
		483EB36E284362480098F10A /* JavaScriptCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 483EB36D284362480098F10A /* JavaScriptCore.framework */; };
		486CA5CC2934462000D04ABD /* CBFRJMStaticContentTextFieldTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 486CA5C32934462000D04ABD /* CBFRJMStaticContentTextFieldTableViewCell.m */; };
		486CA5CD2934462000D04ABD /* CBFRJMStaticContentTableViewSection.m in Sources */ = {isa = PBXBuildFile; fileRef = 486CA5C52934462000D04ABD /* CBFRJMStaticContentTableViewSection.m */; };
		486CA5CE2934462000D04ABD /* CBFRJMStaticContentTableViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 486CA5C62934462000D04ABD /* CBFRJMStaticContentTableViewController.m */; };
		486CA5CF2934462000D04ABD /* CBFRJMStaticContentTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 486CA5C82934462000D04ABD /* CBFRJMStaticContentTableViewCell.m */; };
		486CA5DA2934463100D04ABD /* CBFRMailComposerController.m in Sources */ = {isa = PBXBuildFile; fileRef = 486CA5D12934463000D04ABD /* CBFRMailComposerController.m */; };
		486CA5DB2934463100D04ABD /* CBFRUIApplication+VersionInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = 486CA5D22934463000D04ABD /* CBFRUIApplication+VersionInfo.m */; };
		486CA5DC2934463100D04ABD /* CBFRMHAboutViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 486CA5D42934463000D04ABD /* CBFRMHAboutViewController.m */; };
		486CA5DD2934463100D04ABD /* CBFRMHAboutViewController2.m in Sources */ = {isa = PBXBuildFile; fileRef = 486CA5D92934463000D04ABD /* CBFRMHAboutViewController2.m */; };
		48DB70CD2A56C732009FCEEC /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 48DB70CC2A56C72F009FCEEC /* GoogleService-Info.plist */; };
		8A84ACD8358E5E9AC1C2AE7D /* libPods-CameraBoxFR.a in Frameworks */ = {isa = PBXBuildFile; fileRef = DCF2C41636901192981466BF /* libPods-CameraBoxFR.a */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		0A51BFD46E3D57A4D9D650C4 /* Pods-CameraBoxFR.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-CameraBoxFR.release.xcconfig"; path = "Target Support Files/Pods-CameraBoxFR/Pods-CameraBoxFR.release.xcconfig"; sourceTree = "<group>"; };
		0A811C901DAB32280045F266 /* cbfr_Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = cbfr_Images.xcassets; path = CameraBox/cbfr_Images.xcassets; sourceTree = "<group>"; };
		0A811C9E1DAB34A80045F266 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = LaunchScreen.storyboard; sourceTree = "<group>"; };
		0A811D3A1DAB4B280045F266 /* libxml2.2.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libxml2.2.tbd; path = usr/lib/libxml2.2.tbd; sourceTree = SDKROOT; };
		0A811D3C1DAB4B490045F266 /* MessageUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MessageUI.framework; path = System/Library/Frameworks/MessageUI.framework; sourceTree = SDKROOT; };
		0ABCF70A1DACB85900EFF386 /* AudioToolbox.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AudioToolbox.framework; path = System/Library/Frameworks/AudioToolbox.framework; sourceTree = SDKROOT; };
		0ABCF70C1DACB86D00EFF386 /* AVFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVFoundation.framework; path = System/Library/Frameworks/AVFoundation.framework; sourceTree = SDKROOT; };
		0ABCF70E1DACB88000EFF386 /* CoreTelephony.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreTelephony.framework; path = System/Library/Frameworks/CoreTelephony.framework; sourceTree = SDKROOT; };
		0ABCF7101DACBA3A00EFF386 /* StoreKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = StoreKit.framework; path = System/Library/Frameworks/StoreKit.framework; sourceTree = SDKROOT; };
		0ABCF7121DACBA4E00EFF386 /* CoreAudio.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreAudio.framework; path = System/Library/Frameworks/CoreAudio.framework; sourceTree = SDKROOT; };
		0ABCF7141DACBA7D00EFF386 /* CoreMedia.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMedia.framework; path = System/Library/Frameworks/CoreMedia.framework; sourceTree = SDKROOT; };
		0ABCF7161DACBA8F00EFF386 /* MediaPlayer.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MediaPlayer.framework; path = System/Library/Frameworks/MediaPlayer.framework; sourceTree = SDKROOT; };
		0ABCF7181DACBB0100EFF386 /* QuartzCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = QuartzCore.framework; path = System/Library/Frameworks/QuartzCore.framework; sourceTree = SDKROOT; };
		0ABCF71A1DACBB4F00EFF386 /* SystemConfiguration.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SystemConfiguration.framework; path = System/Library/Frameworks/SystemConfiguration.framework; sourceTree = SDKROOT; };
		0ABCF71C1DACBB6B00EFF386 /* AdSupport.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AdSupport.framework; path = System/Library/Frameworks/AdSupport.framework; sourceTree = SDKROOT; };
		0ABCF71E1DACBBAA00EFF386 /* EventKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = EventKit.framework; path = System/Library/Frameworks/EventKit.framework; sourceTree = SDKROOT; };
		0ABCF7201DACBBB000EFF386 /* EventKitUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = EventKitUI.framework; path = System/Library/Frameworks/EventKitUI.framework; sourceTree = SDKROOT; };
		0ABCF7221DACCDF400EFF386 /* OpenGLES.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = OpenGLES.framework; path = System/Library/Frameworks/OpenGLES.framework; sourceTree = SDKROOT; };
		0ABCF7241DACCE3600EFF386 /* SafariServices.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SafariServices.framework; path = System/Library/Frameworks/SafariServices.framework; sourceTree = SDKROOT; };
		0ABCF7261DACCE4D00EFF386 /* CoreMotion.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMotion.framework; path = System/Library/Frameworks/CoreMotion.framework; sourceTree = SDKROOT; };
		0ABCF7281DACCE8A00EFF386 /* GLKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = GLKit.framework; path = System/Library/Frameworks/GLKit.framework; sourceTree = SDKROOT; };
		0ABCF72A1DACCEB000EFF386 /* CoreVideo.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreVideo.framework; path = System/Library/Frameworks/CoreVideo.framework; sourceTree = SDKROOT; };
		0ABCF72C1DACCED800EFF386 /* MobileCoreServices.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MobileCoreServices.framework; path = System/Library/Frameworks/MobileCoreServices.framework; sourceTree = SDKROOT; };
		1D30AB110D05D00D00671497 /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = System/Library/Frameworks/Foundation.framework; sourceTree = SDKROOT; };
		1D3623240D0F684500981E51 /* CBFRbwCameraAppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CBFRbwCameraAppDelegate.h; sourceTree = "<group>"; };
		1D3623250D0F684500981E51 /* CBFRbwCameraAppDelegate.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CBFRbwCameraAppDelegate.m; sourceTree = "<group>"; };
		1D6058910D05DD3D006BFB54 /* CameraBoxFR.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = CameraBoxFR.app; sourceTree = BUILT_PRODUCTS_DIR; };
		1DF5F4DF0D08C38300B7A737 /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = System/Library/Frameworks/UIKit.framework; sourceTree = SDKROOT; };
		280E754B0DD40C5E005A515E /* CBFRMainView.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = CBFRMainView.xib; sourceTree = "<group>"; };
		280E754C0DD40C5E005A515E /* MainWindow.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = MainWindow.xib; sourceTree = "<group>"; };
		288765580DF743DE002DB57D /* CoreGraphics.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreGraphics.framework; path = System/Library/Frameworks/CoreGraphics.framework; sourceTree = SDKROOT; };
		289233A40DB2D0840083E9F9 /* CBFRMainView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = CBFRMainView.h; path = Classes/CBFRMainView.h; sourceTree = "<group>"; };
		289233A50DB2D0840083E9F9 /* CBFRMainView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = CBFRMainView.m; path = Classes/CBFRMainView.m; sourceTree = "<group>"; };
		289233A80DB2D0DB0083E9F9 /* CBFRMainViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = CBFRMainViewController.h; path = Classes/CBFRMainViewController.h; sourceTree = "<group>"; };
		289233A90DB2D0DB0083E9F9 /* CBFRMainViewController.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; name = CBFRMainViewController.mm; path = Classes/CBFRMainViewController.mm; sourceTree = "<group>"; };
		29B97316FDCFA39411CA2CEA /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		32CA4F630368D1EE00C91783 /* CameraBoxFR_Prefix.pch */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CameraBoxFR_Prefix.pch; sourceTree = "<group>"; };
		33C59237037314D08CAB0B26 /* Pods-CameraBoxFR.distribution.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-CameraBoxFR.distribution.xcconfig"; path = "Target Support Files/Pods-CameraBoxFR/Pods-CameraBoxFR.distribution.xcconfig"; sourceTree = "<group>"; };
		483B20572C20EF6800B24FE6 /* GoogleMobileAds.xcframework */ = {isa = PBXFileReference; expectedSignature = "AppleDeveloperProgram:EQHXZ8M8AV:Google LLC"; lastKnownFileType = wrapper.xcframework; name = GoogleMobileAds.xcframework; path = "Vendor/GoogleMobileAdsSdkiOS-11.6.0/GoogleMobileAdsSdkiOS-11.6.0/GoogleMobileAds.xcframework"; sourceTree = "<group>"; };
		483B205B2C20EF7A00B24FE6 /* UserMessagingPlatform.xcframework */ = {isa = PBXFileReference; expectedSignature = "AppleDeveloperProgram:EQHXZ8M8AV:Google LLC"; lastKnownFileType = wrapper.xcframework; name = UserMessagingPlatform.xcframework; path = "Vendor/GoogleMobileAdsSdkiOS-11.6.0/GoogleMobileAdsSdkiOS-11.6.0/UserMessagingPlatform.xcframework"; sourceTree = "<group>"; };
		483B205E2C20F28000B24FE6 /* CameraBoxFR-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "CameraBoxFR-Bridging-Header.h"; sourceTree = "<group>"; };
		483B205F2C20F28000B24FE6 /* UserMessagingPlatform.xcframework */ = {isa = PBXFileReference; expectedSignature = "AppleDeveloperProgram:EQHXZ8M8AV:Google LLC"; lastKnownFileType = wrapper.xcframework; name = UserMessagingPlatform.xcframework; path = "Vendor/GoogleMobileAdsSdkiOS-11.6.0/GoogleMobileAdsSdkiOS-11.6.0/UserMessagingPlatform.xcframework"; sourceTree = "<group>"; };
		483B20602C20F28000B24FE6 /* GoogleMobileAdsPlaceholder.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; name = GoogleMobileAdsPlaceholder.swift; path = "Vendor/GoogleMobileAdsSdkiOS-11.6.0/GoogleMobileAdsSdkiOS-11.6.0/GoogleMobileAdsPlaceholder.swift"; sourceTree = "<group>"; };
		483B20612C20F28000B24FE6 /* GoogleMobileAds.xcframework */ = {isa = PBXFileReference; expectedSignature = "AppleDeveloperProgram:EQHXZ8M8AV:Google LLC"; lastKnownFileType = wrapper.xcframework; name = GoogleMobileAds.xcframework; path = "Vendor/GoogleMobileAdsSdkiOS-11.6.0/GoogleMobileAdsSdkiOS-11.6.0/GoogleMobileAds.xcframework"; sourceTree = "<group>"; };
		483EB36D284362480098F10A /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
		486CA5C32934462000D04ABD /* CBFRJMStaticContentTextFieldTableViewCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CBFRJMStaticContentTextFieldTableViewCell.m; sourceTree = "<group>"; };
		486CA5C42934462000D04ABD /* CBFRJMStaticContentTableViewCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CBFRJMStaticContentTableViewCell.h; sourceTree = "<group>"; };
		486CA5C52934462000D04ABD /* CBFRJMStaticContentTableViewSection.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CBFRJMStaticContentTableViewSection.m; sourceTree = "<group>"; };
		486CA5C62934462000D04ABD /* CBFRJMStaticContentTableViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CBFRJMStaticContentTableViewController.m; sourceTree = "<group>"; };
		486CA5C72934462000D04ABD /* CBFRJMStaticContentTextFieldTableViewCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CBFRJMStaticContentTextFieldTableViewCell.h; sourceTree = "<group>"; };
		486CA5C82934462000D04ABD /* CBFRJMStaticContentTableViewCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CBFRJMStaticContentTableViewCell.m; sourceTree = "<group>"; };
		486CA5C92934462000D04ABD /* JMStaticContentTableViewBlocks.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JMStaticContentTableViewBlocks.h; sourceTree = "<group>"; };
		486CA5CA2934462000D04ABD /* CBFRJMStaticContentTableViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CBFRJMStaticContentTableViewController.h; sourceTree = "<group>"; };
		486CA5CB2934462000D04ABD /* CBFRJMStaticContentTableViewSection.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CBFRJMStaticContentTableViewSection.h; sourceTree = "<group>"; };
		486CA5D12934463000D04ABD /* CBFRMailComposerController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CBFRMailComposerController.m; sourceTree = "<group>"; };
		486CA5D22934463000D04ABD /* CBFRUIApplication+VersionInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "CBFRUIApplication+VersionInfo.m"; sourceTree = "<group>"; };
		486CA5D32934463000D04ABD /* CBFRMHAboutViewController2.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CBFRMHAboutViewController2.h; sourceTree = "<group>"; };
		486CA5D42934463000D04ABD /* CBFRMHAboutViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CBFRMHAboutViewController.m; sourceTree = "<group>"; };
		486CA5D52934463000D04ABD /* CBFRUIApplication+VersionInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "CBFRUIApplication+VersionInfo.h"; sourceTree = "<group>"; };
		486CA5D62934463000D04ABD /* CBFRMailComposerController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CBFRMailComposerController.h; sourceTree = "<group>"; };
		486CA5D72934463000D04ABD /* MHAboutView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MHAboutView.h; sourceTree = "<group>"; };
		486CA5D82934463000D04ABD /* CBFRMHAboutViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CBFRMHAboutViewController.h; sourceTree = "<group>"; };
		486CA5D92934463000D04ABD /* CBFRMHAboutViewController2.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CBFRMHAboutViewController2.m; sourceTree = "<group>"; };
		48DB70CC2A56C72F009FCEEC /* GoogleService-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		48FCF6162A3304AC001D9566 /* cbfr_IMG_0001.JPG */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = cbfr_IMG_0001.JPG; sourceTree = "<group>"; };
		8D1107310486CEB800E47090 /* CameraBoxFR-Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = "CameraBoxFR-Info.plist"; plistStructureDefinitionIdentifier = "com.apple.xcode.plist.structure-definition.iphone.info-plist"; sourceTree = "<group>"; };
		CC7446A0F931BE8DBC81C673 /* Pods-CameraBoxFR.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-CameraBoxFR.debug.xcconfig"; path = "Target Support Files/Pods-CameraBoxFR/Pods-CameraBoxFR.debug.xcconfig"; sourceTree = "<group>"; };
		DCF2C41636901192981466BF /* libPods-CameraBoxFR.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-CameraBoxFR.a"; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		1D60588F0D05DD3D006BFB54 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0ABCF72D1DACCED800EFF386 /* MobileCoreServices.framework in Frameworks */,
				0ABCF72B1DACCEB000EFF386 /* CoreVideo.framework in Frameworks */,
				483EB36E284362480098F10A /* JavaScriptCore.framework in Frameworks */,
				0ABCF7291DACCE8A00EFF386 /* GLKit.framework in Frameworks */,
				0ABCF7271DACCE4D00EFF386 /* CoreMotion.framework in Frameworks */,
				0ABCF7251DACCE3600EFF386 /* SafariServices.framework in Frameworks */,
				483B20682C20F2C700B24FE6 /* UserMessagingPlatform.xcframework in Frameworks */,
				0ABCF7231DACCDF400EFF386 /* OpenGLES.framework in Frameworks */,
				0ABCF7211DACBBB000EFF386 /* EventKitUI.framework in Frameworks */,
				0ABCF71F1DACBBAA00EFF386 /* EventKit.framework in Frameworks */,
				0ABCF71D1DACBB6B00EFF386 /* AdSupport.framework in Frameworks */,
				0ABCF71B1DACBB4F00EFF386 /* SystemConfiguration.framework in Frameworks */,
				483B20652C20F2B100B24FE6 /* GoogleMobileAds.xcframework in Frameworks */,
				0ABCF7191DACBB0100EFF386 /* QuartzCore.framework in Frameworks */,
				288765590DF743DE002DB57D /* CoreGraphics.framework in Frameworks */,
				0ABCF7171DACBA8F00EFF386 /* MediaPlayer.framework in Frameworks */,
				0ABCF7151DACBA7E00EFF386 /* CoreMedia.framework in Frameworks */,
				0ABCF7131DACBA4E00EFF386 /* CoreAudio.framework in Frameworks */,
				0ABCF70F1DACB88000EFF386 /* CoreTelephony.framework in Frameworks */,
				0ABCF70D1DACB86D00EFF386 /* AVFoundation.framework in Frameworks */,
				0ABCF70B1DACB85900EFF386 /* AudioToolbox.framework in Frameworks */,
				0A811D3D1DAB4B490045F266 /* MessageUI.framework in Frameworks */,
				0A811D3B1DAB4B280045F266 /* libxml2.2.tbd in Frameworks */,
				1D60589F0D05DD5A006BFB54 /* Foundation.framework in Frameworks */,
				1DF5F4E00D08C38300B7A737 /* UIKit.framework in Frameworks */,
				8A84ACD8358E5E9AC1C2AE7D /* libPods-CameraBoxFR.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		080E96DDFE201D6D7F000001 /* Application Delegate */ = {
			isa = PBXGroup;
			children = (
				1D3623240D0F684500981E51 /* CBFRbwCameraAppDelegate.h */,
				1D3623250D0F684500981E51 /* CBFRbwCameraAppDelegate.m */,
			);
			name = "Application Delegate";
			path = Classes;
			sourceTree = "<group>";
		};
		0A811C9C1DAB34A80045F266 /* Base.lproj */ = {
			isa = PBXGroup;
			children = (
				0A811C9D1DAB34A80045F266 /* LaunchScreen.storyboard */,
			);
			path = Base.lproj;
			sourceTree = "<group>";
		};
		0A811CA01DAB49680045F266 /* Vendor */ = {
			isa = PBXGroup;
			children = (
				483B20612C20F28000B24FE6 /* GoogleMobileAds.xcframework */,
				483B20602C20F28000B24FE6 /* GoogleMobileAdsPlaceholder.swift */,
				483B205F2C20F28000B24FE6 /* UserMessagingPlatform.xcframework */,
				486CA5D02934463000D04ABD /* MHAboutView */,
				486CA5C22934462000D04ABD /* JMStaticContentTableViewController */,
				483B205E2C20F28000B24FE6 /* CameraBoxFR-Bridging-Header.h */,
			);
			name = Vendor;
			sourceTree = "<group>";
		};
		0AA6391F102AA4E700DCA250 /* Class */ = {
			isa = PBXGroup;
			children = (
			);
			name = Class;
			sourceTree = "<group>";
		};
		19C28FACFE9D520D11CA2CBB /* Products */ = {
			isa = PBXGroup;
			children = (
				1D6058910D05DD3D006BFB54 /* CameraBoxFR.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		289233A00DB2D0730083E9F9 /* Main View */ = {
			isa = PBXGroup;
			children = (
				289233A40DB2D0840083E9F9 /* CBFRMainView.h */,
				289233A50DB2D0840083E9F9 /* CBFRMainView.m */,
				289233A80DB2D0DB0083E9F9 /* CBFRMainViewController.h */,
				289233A90DB2D0DB0083E9F9 /* CBFRMainViewController.mm */,
			);
			name = "Main View";
			sourceTree = "<group>";
		};
		29B97314FDCFA39411CA2CEA /* CustomTemplate */ = {
			isa = PBXGroup;
			children = (
				48DB70CC2A56C72F009FCEEC /* GoogleService-Info.plist */,
				48FCF6162A3304AC001D9566 /* cbfr_IMG_0001.JPG */,
				0A811CA01DAB49680045F266 /* Vendor */,
				0A811C9C1DAB34A80045F266 /* Base.lproj */,
				0AA6391F102AA4E700DCA250 /* Class */,
				289233A00DB2D0730083E9F9 /* Main View */,
				080E96DDFE201D6D7F000001 /* Application Delegate */,
				29B97315FDCFA39411CA2CEA /* Other Sources */,
				29B97317FDCFA39411CA2CEA /* Resources */,
				29B97323FDCFA39411CA2CEA /* Frameworks */,
				19C28FACFE9D520D11CA2CBB /* Products */,
				3343D342DA1778A588D88768 /* Pods */,
			);
			name = CustomTemplate;
			sourceTree = "<group>";
		};
		29B97315FDCFA39411CA2CEA /* Other Sources */ = {
			isa = PBXGroup;
			children = (
				32CA4F630368D1EE00C91783 /* CameraBoxFR_Prefix.pch */,
				29B97316FDCFA39411CA2CEA /* main.m */,
			);
			name = "Other Sources";
			sourceTree = "<group>";
		};
		29B97317FDCFA39411CA2CEA /* Resources */ = {
			isa = PBXGroup;
			children = (
				0A811C901DAB32280045F266 /* cbfr_Images.xcassets */,
				280E754B0DD40C5E005A515E /* CBFRMainView.xib */,
				280E754C0DD40C5E005A515E /* MainWindow.xib */,
				8D1107310486CEB800E47090 /* CameraBoxFR-Info.plist */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		29B97323FDCFA39411CA2CEA /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				483B205B2C20EF7A00B24FE6 /* UserMessagingPlatform.xcframework */,
				483B20572C20EF6800B24FE6 /* GoogleMobileAds.xcframework */,
				483EB36D284362480098F10A /* JavaScriptCore.framework */,
				0ABCF72C1DACCED800EFF386 /* MobileCoreServices.framework */,
				0ABCF72A1DACCEB000EFF386 /* CoreVideo.framework */,
				0ABCF7281DACCE8A00EFF386 /* GLKit.framework */,
				0ABCF7261DACCE4D00EFF386 /* CoreMotion.framework */,
				0ABCF7241DACCE3600EFF386 /* SafariServices.framework */,
				0ABCF7221DACCDF400EFF386 /* OpenGLES.framework */,
				0ABCF7201DACBBB000EFF386 /* EventKitUI.framework */,
				0ABCF71E1DACBBAA00EFF386 /* EventKit.framework */,
				0ABCF71C1DACBB6B00EFF386 /* AdSupport.framework */,
				0ABCF71A1DACBB4F00EFF386 /* SystemConfiguration.framework */,
				0ABCF7181DACBB0100EFF386 /* QuartzCore.framework */,
				0ABCF7161DACBA8F00EFF386 /* MediaPlayer.framework */,
				0ABCF7141DACBA7D00EFF386 /* CoreMedia.framework */,
				0ABCF7121DACBA4E00EFF386 /* CoreAudio.framework */,
				0ABCF7101DACBA3A00EFF386 /* StoreKit.framework */,
				0ABCF70E1DACB88000EFF386 /* CoreTelephony.framework */,
				0ABCF70C1DACB86D00EFF386 /* AVFoundation.framework */,
				0ABCF70A1DACB85900EFF386 /* AudioToolbox.framework */,
				0A811D3C1DAB4B490045F266 /* MessageUI.framework */,
				0A811D3A1DAB4B280045F266 /* libxml2.2.tbd */,
				1DF5F4DF0D08C38300B7A737 /* UIKit.framework */,
				1D30AB110D05D00D00671497 /* Foundation.framework */,
				288765580DF743DE002DB57D /* CoreGraphics.framework */,
				DCF2C41636901192981466BF /* libPods-CameraBoxFR.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		3343D342DA1778A588D88768 /* Pods */ = {
			isa = PBXGroup;
			children = (
				CC7446A0F931BE8DBC81C673 /* Pods-CameraBoxFR.debug.xcconfig */,
				0A51BFD46E3D57A4D9D650C4 /* Pods-CameraBoxFR.release.xcconfig */,
				33C59237037314D08CAB0B26 /* Pods-CameraBoxFR.distribution.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		486CA5C22934462000D04ABD /* JMStaticContentTableViewController */ = {
			isa = PBXGroup;
			children = (
				486CA5C32934462000D04ABD /* CBFRJMStaticContentTextFieldTableViewCell.m */,
				486CA5C42934462000D04ABD /* CBFRJMStaticContentTableViewCell.h */,
				486CA5C52934462000D04ABD /* CBFRJMStaticContentTableViewSection.m */,
				486CA5C62934462000D04ABD /* CBFRJMStaticContentTableViewController.m */,
				486CA5C72934462000D04ABD /* CBFRJMStaticContentTextFieldTableViewCell.h */,
				486CA5C82934462000D04ABD /* CBFRJMStaticContentTableViewCell.m */,
				486CA5C92934462000D04ABD /* JMStaticContentTableViewBlocks.h */,
				486CA5CA2934462000D04ABD /* CBFRJMStaticContentTableViewController.h */,
				486CA5CB2934462000D04ABD /* CBFRJMStaticContentTableViewSection.h */,
			);
			name = JMStaticContentTableViewController;
			path = Vendor/JMStaticContentTableViewController/JMStaticContentTableViewController;
			sourceTree = "<group>";
		};
		486CA5D02934463000D04ABD /* MHAboutView */ = {
			isa = PBXGroup;
			children = (
				486CA5D12934463000D04ABD /* CBFRMailComposerController.m */,
				486CA5D22934463000D04ABD /* CBFRUIApplication+VersionInfo.m */,
				486CA5D32934463000D04ABD /* CBFRMHAboutViewController2.h */,
				486CA5D42934463000D04ABD /* CBFRMHAboutViewController.m */,
				486CA5D52934463000D04ABD /* CBFRUIApplication+VersionInfo.h */,
				486CA5D62934463000D04ABD /* CBFRMailComposerController.h */,
				486CA5D72934463000D04ABD /* MHAboutView.h */,
				486CA5D82934463000D04ABD /* CBFRMHAboutViewController.h */,
				486CA5D92934463000D04ABD /* CBFRMHAboutViewController2.m */,
			);
			name = MHAboutView;
			path = Vendor/MHAboutView/MHAboutView;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		1D6058900D05DD3D006BFB54 /* CameraBoxFR */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1D6058960D05DD3E006BFB54 /* Build configuration list for PBXNativeTarget "CameraBoxFR" */;
			buildPhases = (
				AFC4457A16780DE1B6B60741 /* [CP] Check Pods Manifest.lock */,
				1D60588D0D05DD3D006BFB54 /* Resources */,
				1D60588E0D05DD3D006BFB54 /* Sources */,
				1D60588F0D05DD3D006BFB54 /* Frameworks */,
				0A811D441DAB8F500045F266 /* ShellScript */,
				00A7D2A050E2ED872478CEA9 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = CameraBoxFR;
			productName = CameraBox;
			productReference = 1D6058910D05DD3D006BFB54 /* CameraBoxFR.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		29B97313FDCFA39411CA2CEA /* Project object */ = {
			isa = PBXProject;
			attributes = {
				TargetAttributes = {
					1D6058900D05DD3D006BFB54 = {
						LastSwiftMigration = 1530;
					};
				};
			};
			buildConfigurationList = C01FCF4E08A954540054247B /* Build configuration list for PBXProject "CameraBoxFR" */;
			compatibilityVersion = "Xcode 3.1";
			developmentRegion = English;
			hasScannedForEncodings = 1;
			knownRegions = (
				English,
				Japanese,
				French,
				German,
				en,
				Base,
			);
			mainGroup = 29B97314FDCFA39411CA2CEA /* CustomTemplate */;
			productRefGroup = 19C28FACFE9D520D11CA2CBB /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				1D6058900D05DD3D006BFB54 /* CameraBoxFR */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		1D60588D0D05DD3D006BFB54 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0A811C911DAB32280045F266 /* cbfr_Images.xcassets in Resources */,
				280E754E0DD40C5E005A515E /* CBFRMainView.xib in Resources */,
				48DB70CD2A56C732009FCEEC /* GoogleService-Info.plist in Resources */,
				0A811C9F1DAB34A80045F266 /* LaunchScreen.storyboard in Resources */,
				280E754F0DD40C5E005A515E /* MainWindow.xib in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00A7D2A050E2ED872478CEA9 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-CameraBoxFR/Pods-CameraBoxFR-resources.sh",
				"${PODS_ROOT}/../Vendor/Easence-EAFeatureGuideView-kw/EAFeatureGuideView/Resources/<EMAIL>",
				"${PODS_ROOT}/../Vendor/Easence-EAFeatureGuideView-kw/EAFeatureGuideView/Resources/<EMAIL>",
				"${PODS_ROOT}/../Vendor/Easence-EAFeatureGuideView-kw/EAFeatureGuideView/Resources/<EMAIL>",
				"${PODS_ROOT}/../Vendor/Easence-EAFeatureGuideView-kw/EAFeatureGuideView/Resources/<EMAIL>",
			);
			name = "[CP] Copy Pods Resources";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/<EMAIL>",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/<EMAIL>",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/<EMAIL>",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/<EMAIL>",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-CameraBoxFR/Pods-CameraBoxFR-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		0A811D441DAB8F500045F266 /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = pwd;
		};
		AFC4457A16780DE1B6B60741 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-CameraBoxFR-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		1D60588E0D05DD3D006BFB54 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				483B20632C20F28000B24FE6 /* GoogleMobileAdsPlaceholder.swift in Sources */,
				486CA5DA2934463100D04ABD /* CBFRMailComposerController.m in Sources */,
				1D60589B0D05DD56006BFB54 /* main.m in Sources */,
				1D3623260D0F684500981E51 /* CBFRbwCameraAppDelegate.m in Sources */,
				289233A70DB2D0840083E9F9 /* CBFRMainView.m in Sources */,
				289233AE0DB2D0DB0083E9F9 /* CBFRMainViewController.mm in Sources */,
				486CA5CE2934462000D04ABD /* CBFRJMStaticContentTableViewController.m in Sources */,
				486CA5CD2934462000D04ABD /* CBFRJMStaticContentTableViewSection.m in Sources */,
				486CA5CF2934462000D04ABD /* CBFRJMStaticContentTableViewCell.m in Sources */,
				486CA5DD2934463100D04ABD /* CBFRMHAboutViewController2.m in Sources */,
				486CA5DC2934463100D04ABD /* CBFRMHAboutViewController.m in Sources */,
				486CA5CC2934462000D04ABD /* CBFRJMStaticContentTextFieldTableViewCell.m in Sources */,
				486CA5DB2934463100D04ABD /* CBFRUIApplication+VersionInfo.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		0A811C9D1DAB34A80045F266 /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				0A811C9E1DAB34A80045F266 /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		0A99129B102D1D3600AB36DC /* Distribution */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_32_BIT)";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				GCC_C_LANGUAGE_STANDARD = c99;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 4.0;
				PREBINDING = NO;
				SDKROOT = iphoneos;
			};
			name = Distribution;
		};
		0A99129C102D1D3600AB36DC /* Distribution */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 33C59237037314D08CAB0B26 /* Pods-CameraBoxFR.distribution.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ARCHS = "$(ARCHS_STANDARD)";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = NO;
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				COPY_PHASE_STRIP = YES;
				DEVELOPMENT_TEAM = E3KGS57AD6;
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = E3KGS57AD6;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"Vendor/GoogleMobileAdsSdkiOS-11.6.0/GoogleMobileAdsSdkiOS-11.6.0",
				);
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = CameraBoxFR_Prefix.pch;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"COCOAPODS=1",
				);
				HEADER_SEARCH_PATHS = (
					"$(SDKROOT)/usr/include/libxml2",
					"$(inherited)",
				);
				INFOPLIST_FILE = "CameraBoxFR-Info.plist";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.photography";
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				OTHER_LDFLAGS = (
					"-ObjC",
					"$(inherited)",
					"-ld64",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.wanghaiwen.free.CameraBox;
				PRODUCT_NAME = CameraBoxFR;
				PROVISIONING_PROFILE = "6fbfda5e-b773-4a40-9393-4562fa6751c1";
				"PROVISIONING_PROFILE[sdk=iphoneos*]" = "DC816EB7-7B68-430A-A31B-F1B4A5C7F04C";
				PROVISIONING_PROFILE_SPECIFIER = "chenying753 dev";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = chenying753_dev_3;
				SWIFT_OBJC_BRIDGING_HEADER = "CameraBoxFR-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Distribution;
		};
		1D6058940D05DD3E006BFB54 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = CC7446A0F931BE8DBC81C673 /* Pods-CameraBoxFR.debug.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ARCHS = "$(ARCHS_STANDARD)";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = NO;
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				COPY_PHASE_STRIP = NO;
				DEVELOPMENT_TEAM = E3KGS57AD6;
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = E3KGS57AD6;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"Vendor/GoogleMobileAdsSdkiOS-11.6.0/GoogleMobileAdsSdkiOS-11.6.0",
				);
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = CameraBoxFR_Prefix.pch;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"CBFR_KW_DEBUG=1",
					"$(inherited)",
					"CBFR_KW_ADMOB_TEST=1",
				);
				HEADER_SEARCH_PATHS = (
					"$(SDKROOT)/usr/include/libxml2",
					"$(inherited)",
				);
				INFOPLIST_FILE = "CameraBoxFR-Info.plist";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.photography";
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_LDFLAGS = (
					"-ObjC",
					"$(inherited)",
					"-ld64",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.wanghaiwen.free.CameraBox;
				PRODUCT_NAME = CameraBoxFR;
				PROVISIONING_PROFILE = "04c207a8-c249-4bdb-b0ca-828925d96081";
				PROVISIONING_PROFILE_SPECIFIER = "chenying753 dev";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = chenying753_dev_3;
				SWIFT_OBJC_BRIDGING_HEADER = "CameraBoxFR-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		1D6058950D05DD3E006BFB54 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 0A51BFD46E3D57A4D9D650C4 /* Pods-CameraBoxFR.release.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ARCHS = "$(ARCHS_STANDARD)";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = NO;
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				COPY_PHASE_STRIP = YES;
				DEVELOPMENT_TEAM = E3KGS57AD6;
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = E3KGS57AD6;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"Vendor/GoogleMobileAdsSdkiOS-11.6.0/GoogleMobileAdsSdkiOS-11.6.0",
				);
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = CameraBoxFR_Prefix.pch;
				HEADER_SEARCH_PATHS = (
					"$(SDKROOT)/usr/include/libxml2",
					"$(inherited)",
				);
				INFOPLIST_FILE = "CameraBoxFR-Info.plist";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.photography";
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_LDFLAGS = (
					"-ObjC",
					"$(inherited)",
					"-ld64",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.wanghaiwen.free.CameraBox;
				PRODUCT_NAME = CameraBoxFR;
				PROVISIONING_PROFILE = "04c207a8-c249-4bdb-b0ca-828925d96081";
				PROVISIONING_PROFILE_SPECIFIER = "chenying753 dev";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = chenying753_dev_3;
				SWIFT_OBJC_BRIDGING_HEADER = "CameraBoxFR-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		C01FCF4F08A954540054247B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_32_BIT)";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				GCC_C_LANGUAGE_STANDARD = c99;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 4.0;
				PREBINDING = NO;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		C01FCF5008A954540054247B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_32_BIT)";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				GCC_C_LANGUAGE_STANDARD = c99;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 4.0;
				PREBINDING = NO;
				SDKROOT = iphoneos;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		1D6058960D05DD3E006BFB54 /* Build configuration list for PBXNativeTarget "CameraBoxFR" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1D6058940D05DD3E006BFB54 /* Debug */,
				1D6058950D05DD3E006BFB54 /* Release */,
				0A99129C102D1D3600AB36DC /* Distribution */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C01FCF4E08A954540054247B /* Build configuration list for PBXProject "CameraBoxFR" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C01FCF4F08A954540054247B /* Debug */,
				C01FCF5008A954540054247B /* Release */,
				0A99129B102D1D3600AB36DC /* Distribution */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 29B97313FDCFA39411CA2CEA /* Project object */;
}
