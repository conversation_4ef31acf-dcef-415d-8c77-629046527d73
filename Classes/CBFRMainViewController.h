#ifdef __cplusplus
extern "C" {
#endif
#import <GoogleMobileAds/GADBannerView.h>
#import <GoogleMobileAds/GoogleMobileAds.h>
#ifdef __cplusplus
}
#endif
@interface CBFRMainViewController : UIViewController <UIActionSheetDelegate,GADBannerViewDelegate> {
	CGImageRef			cbfr_original;
	CGImageRef			current;
	int					_flash;
	IBOutlet	UILabel			*_filter_name_label;
	IBOutlet	UINavigationBar			*_nav_bar;
    UIPopoverController *popover;
    IBOutlet UIView * adParent;
#if ENABLE_ADMOB
    GADBannerView *bannerView_;
#endif
}
@property (nonatomic, strong) UIAlertView *myAlert;
@property (nonatomic, assign) CGImageRef cbfr_original;
@property (nonatomic, assign) CGImageRef current;
@property (nonatomic, assign) int _flash;
- (IBAction)cbfr_showInfo;
- (IBAction)cbfr_loadFromCamera;
- (IBAction)cbfr_loadFromLibrary;
- (IBAction)loadPic;
- (IBAction)cbfr_saveToLibrary;
- (IBAction)cbfr_delImage;
- (IBAction)cbfr_flashPlus;
- (IBAction)cbfr_flashMinus;
- (IBAction)cbfr_showOriginal;
- (IBAction)cbfr_convert2BW;
- (IBAction)flip;
- (IBAction)invert;
- (IBAction)sepia;
- (IBAction)cbfr_nextFilter;
- (IBAction)cbfr_prevFilter;
- (CGImageRef)cbfr_convertImage2BW:(CGImageRef)abgrImageRef;
- (void)cbfr_showImage:(CGImageRef)img;
@property( nonatomic,strong) IBOutlet UIView *view2;
- (void) processAction: (SEL) sel1;
-(void)cbfr_DismissPopver;
@end
