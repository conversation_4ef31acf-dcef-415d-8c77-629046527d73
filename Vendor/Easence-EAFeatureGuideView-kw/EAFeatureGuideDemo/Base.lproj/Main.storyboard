<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="10117" systemVersion="15G31" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" initialViewController="Q2J-6d-VWE">
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="10085"/>
    </dependencies>
    <scenes>
        <!--Normal-->
        <scene sceneID="tne-QT-ifu">
            <objects>
                <viewController id="BYZ-38-t0r" customClass="ViewController" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="IBQ-n7-xuD"/>
                        <viewControllerLayoutGuide type="bottom" id="aEC-f2-JrJ"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="jNn-pe-zqt">
                        <rect key="frame" x="0.0" y="0.0" width="320" height="568"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        <subviews>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="rpj-uG-0Zg">
                                <rect key="frame" x="20" y="104" width="49" height="20"/>
                                <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                <state key="normal" title="Button" backgroundImage="icon_ea_background"/>
                            </button>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="zWP-0j-FSG">
                                <rect key="frame" x="124" y="104" width="72" height="72"/>
                                <color key="backgroundColor" red="0.98823529409999999" green="0.094117647060000004" blue="0.1019607843" alpha="1" colorSpace="calibratedRGB"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="72" id="cCz-cd-jZw"/>
                                    <constraint firstAttribute="height" constant="72" id="rta-od-OPa"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="PEy-CK-nNS">
                                <rect key="frame" x="247" y="91" width="37" height="37"/>
                                <color key="backgroundColor" red="0.59999999999999998" green="0.59999999999999998" blue="0.59999999999999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="37" id="4zi-ay-nDc"/>
                                    <constraint firstAttribute="width" constant="37" id="t1z-km-bc7"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="SED-ab-rw0">
                                <rect key="frame" x="8" y="222" width="43" height="41"/>
                                <color key="backgroundColor" red="0.78823529410000004" green="0.61568627450000002" blue="0.0" alpha="1" colorSpace="calibratedRGB"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="43" id="3NI-Fp-pnb"/>
                                    <constraint firstAttribute="height" constant="41" id="z3B-eX-FRg"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Q17-pq-i5u">
                                <rect key="frame" x="264" y="264" width="42" height="40"/>
                                <color key="backgroundColor" red="0.78823529410000004" green="0.61568627450000002" blue="0.0" alpha="1" colorSpace="calibratedRGB"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="42" id="7MR-mm-opQ"/>
                                    <constraint firstAttribute="height" constant="40" id="9RX-Be-YHO"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="4VI-sV-saW">
                                <rect key="frame" x="266" y="464" width="40" height="40"/>
                                <color key="backgroundColor" red="0.78823529410000004" green="0.61568627450000002" blue="0.0" alpha="1" colorSpace="calibratedRGB"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="40" id="LwD-MS-WCh"/>
                                    <constraint firstAttribute="width" constant="40" id="qe1-3J-TyJ"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="g4x-Xz-kO7">
                                <rect key="frame" x="8" y="438" width="42" height="40"/>
                                <color key="backgroundColor" red="0.78823529410000004" green="0.61568627450000002" blue="0.0" alpha="1" colorSpace="calibratedRGB"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="40" id="9ch-Pq-Oqg"/>
                                    <constraint firstAttribute="width" constant="42" id="wdN-sw-bKa"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="dwb-Qa-5WS">
                                <rect key="frame" x="139" y="404" width="42" height="40"/>
                                <color key="backgroundColor" red="0.78823529410000004" green="0.61568627450000002" blue="0.0" alpha="1" colorSpace="calibratedRGB"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="40" id="SNR-c8-hqL"/>
                                    <constraint firstAttribute="width" constant="42" id="sxD-5E-ODt"/>
                                </constraints>
                            </view>
                        </subviews>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                        <constraints>
                            <constraint firstItem="aEC-f2-JrJ" firstAttribute="top" secondItem="g4x-Xz-kO7" secondAttribute="bottom" constant="41" id="14c-eb-HhP"/>
                            <constraint firstAttribute="trailing" secondItem="4VI-sV-saW" secondAttribute="trailing" constant="14" id="1zi-h2-gfZ"/>
                            <constraint firstItem="g4x-Xz-kO7" firstAttribute="leading" secondItem="jNn-pe-zqt" secondAttribute="leading" constant="8" id="28c-P8-KwT"/>
                            <constraint firstAttribute="trailing" secondItem="PEy-CK-nNS" secondAttribute="trailing" constant="36" id="3nu-bX-boR"/>
                            <constraint firstItem="zWP-0j-FSG" firstAttribute="top" secondItem="IBQ-n7-xuD" secondAttribute="bottom" constant="40" id="69N-FH-ait"/>
                            <constraint firstItem="rpj-uG-0Zg" firstAttribute="leading" secondItem="jNn-pe-zqt" secondAttribute="leading" constant="20" id="IzL-JK-V83"/>
                            <constraint firstItem="SED-ab-rw0" firstAttribute="leading" secondItem="jNn-pe-zqt" secondAttribute="leading" constant="8" id="QHd-LH-NBp"/>
                            <constraint firstItem="zWP-0j-FSG" firstAttribute="centerX" secondItem="jNn-pe-zqt" secondAttribute="centerX" id="QIW-t7-DrN"/>
                            <constraint firstItem="rpj-uG-0Zg" firstAttribute="top" secondItem="IBQ-n7-xuD" secondAttribute="bottom" constant="40" id="QOE-Qz-CJG"/>
                            <constraint firstItem="aEC-f2-JrJ" firstAttribute="top" secondItem="4VI-sV-saW" secondAttribute="bottom" constant="15" id="Qn1-dh-WIQ"/>
                            <constraint firstItem="SED-ab-rw0" firstAttribute="top" secondItem="rpj-uG-0Zg" secondAttribute="bottom" constant="98" id="bqr-qf-kxu"/>
                            <constraint firstItem="aEC-f2-JrJ" firstAttribute="top" secondItem="dwb-Qa-5WS" secondAttribute="bottom" constant="75" id="cqG-HH-I2i"/>
                            <constraint firstItem="dwb-Qa-5WS" firstAttribute="centerX" secondItem="jNn-pe-zqt" secondAttribute="centerX" id="fRa-L7-0NP"/>
                            <constraint firstAttribute="trailing" secondItem="Q17-pq-i5u" secondAttribute="trailing" constant="14" id="gU9-4h-6ZC"/>
                            <constraint firstItem="Q17-pq-i5u" firstAttribute="centerY" secondItem="jNn-pe-zqt" secondAttribute="centerY" id="hQT-0l-jSe"/>
                            <constraint firstItem="PEy-CK-nNS" firstAttribute="top" secondItem="IBQ-n7-xuD" secondAttribute="bottom" constant="27" id="s40-8r-Jxe"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" title="Normal" id="nWg-QB-5Hc"/>
                    <connections>
                        <outlet property="bottomView" destination="dwb-Qa-5WS" id="Gq3-id-xlA"/>
                        <outlet property="button" destination="rpj-uG-0Zg" id="zgf-9O-Apl"/>
                        <outlet property="leftBottomView" destination="g4x-Xz-kO7" id="P4d-8s-fHo"/>
                        <outlet property="leftView" destination="SED-ab-rw0" id="In9-o0-ONm"/>
                        <outlet property="rightBottomView" destination="4VI-sV-saW" id="H1Z-DX-yx9"/>
                        <outlet property="rightView" destination="Q17-pq-i5u" id="hem-yb-v9N"/>
                        <outlet property="upMidView" destination="zWP-0j-FSG" id="JXw-NF-8qa"/>
                        <outlet property="upRightView" destination="PEy-CK-nNS" id="QtX-Ox-c2M"/>
                        <outlet property="view" destination="jNn-pe-zqt" id="WWM-qz-hIq"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="dkx-z0-nzr" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1202" y="92"/>
        </scene>
        <!--TableView-->
        <scene sceneID="TYO-iT-R9a">
            <objects>
                <tableViewController id="5Fg-Dr-YjY" customClass="TableViewController" sceneMemberID="viewController">
                    <tableView key="view" clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="static" style="plain" separatorStyle="default" rowHeight="44" sectionHeaderHeight="28" sectionFooterHeight="28" id="fZF-CS-N56">
                        <rect key="frame" x="0.0" y="0.0" width="320" height="568"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                        <sections>
                            <tableViewSection id="dqf-f4-1jn">
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" id="L8v-ze-YmH">
                                        <rect key="frame" x="0.0" y="64" width="320" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="L8v-ze-YmH" id="1RH-Ww-xH7">
                                            <rect key="frame" x="0.0" y="0.0" width="320" height="43.5"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="calibratedWhite"/>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="0.1960784314" green="0.73725490199999999" blue="0.25098039220000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" id="xKB-J7-D3t">
                                        <rect key="frame" x="0.0" y="108" width="320" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="xKB-J7-D3t" id="1oh-Dk-ELu">
                                            <rect key="frame" x="0.0" y="0.0" width="320" height="43.5"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="calibratedWhite"/>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="0.98823529409999999" green="0.094117647060000004" blue="0.1019607843" alpha="1" colorSpace="calibratedRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" id="P8R-D7-1yl">
                                        <rect key="frame" x="0.0" y="152" width="320" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="P8R-D7-1yl" id="x7s-Vk-rD2">
                                            <rect key="frame" x="0.0" y="0.0" width="320" height="43.5"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="calibratedWhite"/>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="0.1960784314" green="0.73725490199999999" blue="0.25098039220000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" id="Blh-FH-3yg">
                                        <rect key="frame" x="0.0" y="196" width="320" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="Blh-FH-3yg" id="bg7-JP-31e">
                                            <rect key="frame" x="0.0" y="0.0" width="320" height="43.5"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="calibratedWhite"/>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="0.98823529409999999" green="0.094117647060000004" blue="0.1019607843" alpha="1" colorSpace="calibratedRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" id="x9Q-27-pxi">
                                        <rect key="frame" x="0.0" y="240" width="320" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="x9Q-27-pxi" id="l02-ol-JnD">
                                            <rect key="frame" x="0.0" y="0.0" width="320" height="43.5"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="calibratedWhite"/>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="0.1960784314" green="0.73725490199999999" blue="0.25098039220000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" id="3N1-7s-8Hi">
                                        <rect key="frame" x="0.0" y="284" width="320" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="3N1-7s-8Hi" id="aSZ-Ky-IaC">
                                            <rect key="frame" x="0.0" y="0.0" width="320" height="43.5"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="calibratedWhite"/>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="0.98823529409999999" green="0.094117647060000004" blue="0.1019607843" alpha="1" colorSpace="calibratedRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" id="L04-r3-Mvv">
                                        <rect key="frame" x="0.0" y="328" width="320" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="L04-r3-Mvv" id="aAl-MX-Sm7">
                                            <rect key="frame" x="0.0" y="0.0" width="320" height="43.5"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="calibratedWhite"/>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="0.1960784314" green="0.73725490199999999" blue="0.25098039220000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" id="2IO-52-O2L">
                                        <rect key="frame" x="0.0" y="372" width="320" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="2IO-52-O2L" id="pUW-sG-32D">
                                            <rect key="frame" x="0.0" y="0.0" width="320" height="43.5"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="calibratedWhite"/>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="0.98823529409999999" green="0.094117647060000004" blue="0.1019607843" alpha="1" colorSpace="calibratedRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" id="QOL-a9-fSA">
                                        <rect key="frame" x="0.0" y="416" width="320" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="QOL-a9-fSA" id="fCP-wC-X4m">
                                            <rect key="frame" x="0.0" y="0.0" width="320" height="43.5"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="calibratedWhite"/>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="0.1960784314" green="0.73725490199999999" blue="0.25098039220000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" id="ohR-nz-Os9">
                                        <rect key="frame" x="0.0" y="460" width="320" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="ohR-nz-Os9" id="prR-kR-kZT">
                                            <rect key="frame" x="0.0" y="0.0" width="320" height="43.5"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="calibratedWhite"/>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="0.98823529409999999" green="0.094117647060000004" blue="0.1019607843" alpha="1" colorSpace="calibratedRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" id="ftd-3g-1he">
                                        <rect key="frame" x="0.0" y="504" width="320" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="ftd-3g-1he" id="m2Q-G9-mdy">
                                            <rect key="frame" x="0.0" y="0.0" width="320" height="43.5"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="calibratedWhite"/>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="0.1960784314" green="0.73725490199999999" blue="0.25098039220000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" id="EFn-tZ-RHD">
                                        <rect key="frame" x="0.0" y="548" width="320" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="EFn-tZ-RHD" id="LDw-Dg-vLq">
                                            <rect key="frame" x="0.0" y="0.0" width="320" height="43.5"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="calibratedWhite"/>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="0.98823529409999999" green="0.094117647060000004" blue="0.1019607843" alpha="1" colorSpace="calibratedRGB"/>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                        </sections>
                        <connections>
                            <outlet property="dataSource" destination="5Fg-Dr-YjY" id="HxY-jf-mXh"/>
                            <outlet property="delegate" destination="5Fg-Dr-YjY" id="4wi-LE-No6"/>
                        </connections>
                    </tableView>
                    <navigationItem key="navigationItem" title="TableView" id="KIB-FB-0ib">
                        <barButtonItem key="rightBarButtonItem" title="Item" id="0kw-T1-LTg">
                            <connections>
                                <segue destination="BYZ-38-t0r" kind="push" id="89G-eg-n6l"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <connections>
                        <outlet property="exampleCell" destination="Blh-FH-3yg" id="Zc4-bo-5FN"/>
                    </connections>
                </tableViewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="pFP-g4-cxG" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1202" y="743"/>
        </scene>
        <!--Tab Bar Controller-->
        <scene sceneID="FdL-DS-HYk">
            <objects>
                <tabBarController automaticallyAdjustsScrollViewInsets="NO" id="Q2J-6d-VWE" sceneMemberID="viewController">
                    <toolbarItems/>
                    <tabBar key="tabBar" contentMode="scaleToFill" id="mHs-Zf-n8M">
                        <autoresizingMask key="autoresizingMask"/>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="calibratedWhite"/>
                    </tabBar>
                    <connections>
                        <segue destination="C0q-M5-5DK" kind="relationship" relationship="viewControllers" id="HaK-uE-5o8"/>
                        <segue destination="cNg-cY-TmK" kind="relationship" relationship="viewControllers" id="kMZ-hZ-Vky"/>
                    </connections>
                </tabBarController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="4VI-K5-BC3" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="134" y="301"/>
        </scene>
        <!--Navigation Controller-->
        <scene sceneID="wP9-Iy-86a">
            <objects>
                <navigationController automaticallyAdjustsScrollViewInsets="NO" id="C0q-M5-5DK" sceneMemberID="viewController">
                    <tabBarItem key="tabBarItem" systemItem="topRated" id="qCI-T4-Dbx"/>
                    <toolbarItems/>
                    <navigationBar key="navigationBar" contentMode="scaleToFill" id="6H7-XH-KZL">
                        <rect key="frame" x="0.0" y="0.0" width="320" height="44"/>
                        <autoresizingMask key="autoresizingMask"/>
                    </navigationBar>
                    <nil name="viewControllers"/>
                    <connections>
                        <segue destination="BYZ-38-t0r" kind="relationship" relationship="rootViewController" id="mi4-n1-iYH"/>
                    </connections>
                </navigationController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="B7d-cR-cxB" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="670" y="92"/>
        </scene>
        <!--Navigation Controller-->
        <scene sceneID="8Y9-wY-cpv">
            <objects>
                <navigationController automaticallyAdjustsScrollViewInsets="NO" id="cNg-cY-TmK" sceneMemberID="viewController">
                    <tabBarItem key="tabBarItem" systemItem="recents" id="zx7-zK-STF"/>
                    <toolbarItems/>
                    <navigationBar key="navigationBar" contentMode="scaleToFill" id="lSd-GX-03t">
                        <rect key="frame" x="0.0" y="0.0" width="320" height="44"/>
                        <autoresizingMask key="autoresizingMask"/>
                    </navigationBar>
                    <nil name="viewControllers"/>
                    <connections>
                        <segue destination="5Fg-Dr-YjY" kind="relationship" relationship="rootViewController" id="ryT-8u-zuw"/>
                    </connections>
                </navigationController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="xGM-th-oNR" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="670" y="743"/>
        </scene>
    </scenes>
    <resources>
        <image name="icon_ea_background" width="9" height="8"/>
    </resources>
    <inferredMetricsTieBreakers>
        <segue reference="89G-eg-n6l"/>
    </inferredMetricsTieBreakers>
</document>
