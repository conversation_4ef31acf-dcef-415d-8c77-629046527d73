#import "CBFREAFeatureItem.h"
@interface CBFREAFeatureItem ()
@end
@implementation CBFREAFeatureItem
- (instancetype)initWithFocusView:(UIView *)cbfr_focusView cbfr_focusCornerRadius:(CGFloat) cbfr_focusCornerRadius  cbfr_focusInsets:(UIEdgeInsets) cbfr_focusInsets
{
    self = [super init];
    if (self) {
        _cbfr_focusView = cbfr_focusView;
        self.cbfr_focusCornerRadius = cbfr_focusCornerRadius;
        self.cbfr_focusInsets = cbfr_focusInsets;
    }
    return self;
}
- (instancetype)initWithFocusRect:(CGRect)rect cbfr_focusCornerRadius:(CGFloat) cbfr_focusCornerRadius  cbfr_focusInsets:(UIEdgeInsets) cbfr_focusInsets
{
    self = [super init];
    if (self) {
        _cbfr_focusRect = rect;
        self.cbfr_focusCornerRadius = cbfr_focusCornerRadius;
        self.cbfr_focusInsets = cbfr_focusInsets;
    }
    return self;
}
@end
