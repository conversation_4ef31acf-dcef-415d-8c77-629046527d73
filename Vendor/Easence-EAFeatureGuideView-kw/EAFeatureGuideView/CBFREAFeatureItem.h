#import <UIKit/UIKit.h>
typedef NS_ENUM(NSUInteger, EAFeatureItemAlignmentPriority) {
    EAFeatureItemAlignmentBottomFirst, 
    EAFeatureItemAlignmentTopFirst, 
};
@interface CBFREAFeatureItem : NSObject
@property (nonatomic, strong ,readonly) UIView *cbfr_focusView;
@property (nonatomic, assign ,readonly) CGRect cbfr_focusRect;
@property (nonatomic, assign) CGFloat cbfr_focusCornerRadius;
@property (nonatomic, assign) UIEdgeInsets cbfr_focusInsets;
@property (nonatomic, strong) NSString *cbfr_introduce;
@property (nonatomic, strong) UIFont *cbfr_introduceFont;
@property (nonatomic, strong) UIColor *cbfr_introduceTextColor;
@property (nonatomic ,assign) EAFeatureItemAlignmentPriority cbfr_introduceAlignmentPriority;
@property (nonatomic, copy) void(^action)(id sender);
@property (nonatomic, copy) NSString *cbfr_actionTitle;
@property (nonatomic, copy) NSString *cbfr_indicatorImageName;
@property (nonatomic, copy) NSString *cbfr_buttonBackgroundImageName;
- (instancetype)initWithFocusView:(UIView *)cbfr_focusView cbfr_focusCornerRadius:(CGFloat) cbfr_focusCornerRadius  cbfr_focusInsets:(UIEdgeInsets) cbfr_focusInsets;
- (instancetype)initWithFocusRect:(CGRect)rect cbfr_focusCornerRadius:(CGFloat) cbfr_focusCornerRadius  cbfr_focusInsets:(UIEdgeInsets) cbfr_focusInsets;
@end
