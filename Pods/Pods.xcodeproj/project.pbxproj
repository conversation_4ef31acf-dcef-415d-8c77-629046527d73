// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 60;
	objects = {

/* Begin PBXBuildFile section */
		1695D21E15842C6BE36C39E2FD089FD0 /* CBFREAFeatureItem.m in Sources */ = {isa = PBXBuildFile; fileRef = 12C755D63F21C895E7D472BE9717F8BC /* CBFREAFeatureItem.m */; };
		19D687B2D06AA0B8215DB8E3672ECD1B /* Pods-CameraBoxFR-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = ******************************** /* Pods-CameraBoxFR-dummy.m */; };
		******************************** /* CBFREAFeatureItem.h in Headers */ = {isa = PBXBuildFile; fileRef = 7BE12629C014FF6124AF49E00DB7E5F5 /* CBFREAFeatureItem.h */; settings = {ATTRIBUTES = (Project, ); }; };
		AEE8CA7E9DD135696741F2C4A874BF2B /* CBFRUIView+EAFeatureGuideView.m in Sources */ = {isa = PBXBuildFile; fileRef = 7CC91FBCEEAB9910B7A40F3A72DE78E4 /* CBFRUIView+EAFeatureGuideView.m */; };
		B34888229E55374C40B60DDC03795CBC /* CBFRUIView+EAFeatureGuideView.h in Headers */ = {isa = PBXBuildFile; fileRef = 7CA18F5F592F99C3A50C5DB9891DCC9B /* CBFRUIView+EAFeatureGuideView.h */; settings = {ATTRIBUTES = (Project, ); }; };
		EB0E0073031982685DE160F82190D944 /* EAFeatureGuideView-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 0B89455C17725FEBBE6CDC15A34D0EC8 /* EAFeatureGuideView-dummy.m */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		54C8B442E14E07063DA0F5FAA56EFA02 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 17E456567E0A634834816A1A2888E661;
			remoteInfo = EAFeatureGuideView;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		******************************** /* Pods-CameraBoxFR.distribution.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-CameraBoxFR.distribution.xcconfig"; sourceTree = "<group>"; };
		******************************** /* Pods-CameraBoxFR-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-CameraBoxFR-dummy.m"; sourceTree = "<group>"; };
		0B89455C17725FEBBE6CDC15A34D0EC8 /* EAFeatureGuideView-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "EAFeatureGuideView-dummy.m"; sourceTree = "<group>"; };
		12C755D63F21C895E7D472BE9717F8BC /* CBFREAFeatureItem.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = CBFREAFeatureItem.m; path = EAFeatureGuideView/CBFREAFeatureItem.m; sourceTree = "<group>"; };
		1C2B3813C6D703794D8DEACE7089A116 /* Pods-CameraBoxFR-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-CameraBoxFR-acknowledgements.markdown"; sourceTree = "<group>"; };
		1EAEF48D0AF6EADF167C07AADB33868B /* <EMAIL> */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = "<EMAIL>"; path = "EAFeatureGuideView/Resources/<EMAIL>"; sourceTree = "<group>"; };
		20069F30E9572AE9011D721E4EF2333A /* README.md */ = {isa = PBXFileReference; includeInIndex = 1; path = README.md; sourceTree = "<group>"; };
		******************************** /* Pods-CameraBoxFR-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-CameraBoxFR-acknowledgements.plist"; sourceTree = "<group>"; };
		2FB217E7B9830814D3C54041E29CF06E /* <EMAIL> */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = "<EMAIL>"; path = "EAFeatureGuideView/Resources/<EMAIL>"; sourceTree = "<group>"; };
		******************************** /* Pods-CameraBoxFR.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-CameraBoxFR.release.xcconfig"; sourceTree = "<group>"; };
		******************************** /* Pods-CameraBoxFR-resources.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "Pods-CameraBoxFR-resources.sh"; sourceTree = "<group>"; };
		3DFF26E7E2BDC9DEADC84A5C00B71BE9 /* LICENSE */ = {isa = PBXFileReference; includeInIndex = 1; path = LICENSE; sourceTree = "<group>"; };
		405164E4587D2D90A0E7F095CCC6A29A /* <EMAIL> */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = "<EMAIL>"; path = "EAFeatureGuideView/Resources/<EMAIL>"; sourceTree = "<group>"; };
		5553A517306032347277F5F962CE62A7 /* EAFeatureGuideView-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "EAFeatureGuideView-prefix.pch"; sourceTree = "<group>"; };
		5BAE1414B283A60DF736D7CC7D240A2A /* <EMAIL> */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = "<EMAIL>"; path = "EAFeatureGuideView/Resources/<EMAIL>"; sourceTree = "<group>"; };
		63CE44A7A623A2A356650B926D49FC96 /* EAFeatureGuideView */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; name = EAFeatureGuideView; path = libEAFeatureGuideView.a; sourceTree = BUILT_PRODUCTS_DIR; };
		7BE12629C014FF6124AF49E00DB7E5F5 /* CBFREAFeatureItem.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = CBFREAFeatureItem.h; path = EAFeatureGuideView/CBFREAFeatureItem.h; sourceTree = "<group>"; };
		7CA18F5F592F99C3A50C5DB9891DCC9B /* CBFRUIView+EAFeatureGuideView.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "CBFRUIView+EAFeatureGuideView.h"; path = "EAFeatureGuideView/CBFRUIView+EAFeatureGuideView.h"; sourceTree = "<group>"; };
		7CC91FBCEEAB9910B7A40F3A72DE78E4 /* CBFRUIView+EAFeatureGuideView.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "CBFRUIView+EAFeatureGuideView.m"; path = "EAFeatureGuideView/CBFRUIView+EAFeatureGuideView.m"; sourceTree = "<group>"; };
		******************************** /* Pods-CameraBoxFR */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; name = "Pods-CameraBoxFR"; path = "libPods-CameraBoxFR.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		9B09DA2CE770F576A0ED811CB768A076 /* EAFeatureGuideView.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; path = EAFeatureGuideView.podspec; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		9D940727FF8FB9C785EB98E56350EF41 /* Podfile */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = Podfile; path = ../Podfile; sourceTree = SOURCE_ROOT; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		B23A12F2E5E231B97B1E752511FC72E9 /* EAFeatureGuideView.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = EAFeatureGuideView.release.xcconfig; sourceTree = "<group>"; };
		B5C276EFEDA6BA39DD824FA7C8D2FDA4 /* EAFeatureGuideView.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = EAFeatureGuideView.debug.xcconfig; sourceTree = "<group>"; };
		******************************** /* Pods-CameraBoxFR.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-CameraBoxFR.debug.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		935EB9C3B5580FD5158C71E071CF869F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		ECAAACA0C49EE749D2F3B6D125DA9D49 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		2811DC0443E75F93C53956DC16764DB0 /* Development Pods */ = {
			isa = PBXGroup;
			children = (
				7979E63D84F009731045D265C3FA350E /* EAFeatureGuideView */,
			);
			name = "Development Pods";
			sourceTree = "<group>";
		};
		477799263EA9A3E56DCE028CB404A98E /* Support Files */ = {
			isa = PBXGroup;
			children = (
				0B89455C17725FEBBE6CDC15A34D0EC8 /* EAFeatureGuideView-dummy.m */,
				5553A517306032347277F5F962CE62A7 /* EAFeatureGuideView-prefix.pch */,
				B5C276EFEDA6BA39DD824FA7C8D2FDA4 /* EAFeatureGuideView.debug.xcconfig */,
				B23A12F2E5E231B97B1E752511FC72E9 /* EAFeatureGuideView.release.xcconfig */,
			);
			name = "Support Files";
			path = "../../Pods/Target Support Files/EAFeatureGuideView";
			sourceTree = "<group>";
		};
		7979E63D84F009731045D265C3FA350E /* EAFeatureGuideView */ = {
			isa = PBXGroup;
			children = (
				7BE12629C014FF6124AF49E00DB7E5F5 /* CBFREAFeatureItem.h */,
				12C755D63F21C895E7D472BE9717F8BC /* CBFREAFeatureItem.m */,
				405164E4587D2D90A0E7F095CCC6A29A /* <EMAIL> */,
				2FB217E7B9830814D3C54041E29CF06E /* <EMAIL> */,
				5BAE1414B283A60DF736D7CC7D240A2A /* <EMAIL> */,
				1EAEF48D0AF6EADF167C07AADB33868B /* <EMAIL> */,
				7CA18F5F592F99C3A50C5DB9891DCC9B /* CBFRUIView+EAFeatureGuideView.h */,
				7CC91FBCEEAB9910B7A40F3A72DE78E4 /* CBFRUIView+EAFeatureGuideView.m */,
				D41A98BBDE729EB0DAB9C7B83F3B93CE /* Pod */,
				477799263EA9A3E56DCE028CB404A98E /* Support Files */,
			);
			name = EAFeatureGuideView;
			path = "../Vendor/Easence-EAFeatureGuideView-kw";
			sourceTree = "<group>";
		};
		973001C4A0B62EE795F28ADB24023892 /* Targets Support Files */ = {
			isa = PBXGroup;
			children = (
				FAF73B4281FAF7682B283F19F76001BB /* Pods-CameraBoxFR */,
			);
			name = "Targets Support Files";
			sourceTree = "<group>";
		};
		B4E45A172F02D4EE00822BDDF89B8993 /* Products */ = {
			isa = PBXGroup;
			children = (
				63CE44A7A623A2A356650B926D49FC96 /* EAFeatureGuideView */,
				******************************** /* Pods-CameraBoxFR */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		CF1408CF629C7361332E53B88F7BD30C = {
			isa = PBXGroup;
			children = (
				9D940727FF8FB9C785EB98E56350EF41 /* Podfile */,
				2811DC0443E75F93C53956DC16764DB0 /* Development Pods */,
				D89477F20FB1DE18A04690586D7808C4 /* Frameworks */,
				B4E45A172F02D4EE00822BDDF89B8993 /* Products */,
				973001C4A0B62EE795F28ADB24023892 /* Targets Support Files */,
			);
			sourceTree = "<group>";
		};
		D41A98BBDE729EB0DAB9C7B83F3B93CE /* Pod */ = {
			isa = PBXGroup;
			children = (
				9B09DA2CE770F576A0ED811CB768A076 /* EAFeatureGuideView.podspec */,
				3DFF26E7E2BDC9DEADC84A5C00B71BE9 /* LICENSE */,
				20069F30E9572AE9011D721E4EF2333A /* README.md */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		D89477F20FB1DE18A04690586D7808C4 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		FAF73B4281FAF7682B283F19F76001BB /* Pods-CameraBoxFR */ = {
			isa = PBXGroup;
			children = (
				1C2B3813C6D703794D8DEACE7089A116 /* Pods-CameraBoxFR-acknowledgements.markdown */,
				******************************** /* Pods-CameraBoxFR-acknowledgements.plist */,
				******************************** /* Pods-CameraBoxFR-dummy.m */,
				******************************** /* Pods-CameraBoxFR-resources.sh */,
				******************************** /* Pods-CameraBoxFR.debug.xcconfig */,
				******************************** /* Pods-CameraBoxFR.distribution.xcconfig */,
				******************************** /* Pods-CameraBoxFR.release.xcconfig */,
			);
			name = "Pods-CameraBoxFR";
			path = "Target Support Files/Pods-CameraBoxFR";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		******************************** /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3C84A19F3218CA0A1660B0BB7B8D9FB6 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				******************************** /* CBFREAFeatureItem.h in Headers */,
				B34888229E55374C40B60DDC03795CBC /* CBFRUIView+EAFeatureGuideView.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		17E456567E0A634834816A1A2888E661 /* EAFeatureGuideView */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0BD135C8BA76B006B3FDBF521A86CDB1 /* Build configuration list for PBXNativeTarget "EAFeatureGuideView" */;
			buildPhases = (
				3C84A19F3218CA0A1660B0BB7B8D9FB6 /* Headers */,
				6B16B2300BCC027D6DE024803D1C6C70 /* Sources */,
				935EB9C3B5580FD5158C71E071CF869F /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = EAFeatureGuideView;
			productName = EAFeatureGuideView;
			productReference = 63CE44A7A623A2A356650B926D49FC96 /* EAFeatureGuideView */;
			productType = "com.apple.product-type.library.static";
		};
		28D5B1E572C39581719FF5428BFE2425 /* Pods-CameraBoxFR */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A75DB5EE2883061787F80F3BF0AD71A5 /* Build configuration list for PBXNativeTarget "Pods-CameraBoxFR" */;
			buildPhases = (
				******************************** /* Headers */,
				2163DBE3438B185DC24C42BACF4F5760 /* Sources */,
				ECAAACA0C49EE749D2F3B6D125DA9D49 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				621A9C4348DDF88E4F331744D1C29F56 /* PBXTargetDependency */,
			);
			name = "Pods-CameraBoxFR";
			productName = "Pods-CameraBoxFR";
			productReference = ******************************** /* Pods-CameraBoxFR */;
			productType = "com.apple.product-type.library.static";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		BFDFE7DC352907FC980B868725387E98 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1500;
				LastUpgradeCheck = 1500;
			};
			buildConfigurationList = 4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */;
			compatibilityVersion = "Xcode 15.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				Base,
				en,
			);
			mainGroup = CF1408CF629C7361332E53B88F7BD30C;
			productRefGroup = B4E45A172F02D4EE00822BDDF89B8993 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				17E456567E0A634834816A1A2888E661 /* EAFeatureGuideView */,
				28D5B1E572C39581719FF5428BFE2425 /* Pods-CameraBoxFR */,
			);
		};
/* End PBXProject section */

/* Begin PBXSourcesBuildPhase section */
		2163DBE3438B185DC24C42BACF4F5760 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				19D687B2D06AA0B8215DB8E3672ECD1B /* Pods-CameraBoxFR-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6B16B2300BCC027D6DE024803D1C6C70 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				EB0E0073031982685DE160F82190D944 /* EAFeatureGuideView-dummy.m in Sources */,
				1695D21E15842C6BE36C39E2FD089FD0 /* CBFREAFeatureItem.m in Sources */,
				AEE8CA7E9DD135696741F2C4A874BF2B /* CBFRUIView+EAFeatureGuideView.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		621A9C4348DDF88E4F331744D1C29F56 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = EAFeatureGuideView;
			target = 17E456567E0A634834816A1A2888E661 /* EAFeatureGuideView */;
			targetProxy = 54C8B442E14E07063DA0F5FAA56EFA02 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		47DF1026E77EFB85739E6FF2A63EE8F5 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B23A12F2E5E231B97B1E752511FC72E9 /* EAFeatureGuideView.release.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD)";
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				GCC_PREFIX_HEADER = "Target Support Files/EAFeatureGuideView/EAFeatureGuideView-prefix.pch";
				IPHONEOS_DEPLOYMENT_TARGET = 7.0;
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PRIVATE_HEADERS_FOLDER_PATH = "";
				PRODUCT_MODULE_NAME = EAFeatureGuideView;
				PRODUCT_NAME = EAFeatureGuideView;
				PUBLIC_HEADERS_FOLDER_PATH = "";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		4B82B08A62884B69ABA44C8F60B528E0 /* Distribution */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B23A12F2E5E231B97B1E752511FC72E9 /* EAFeatureGuideView.release.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD)";
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				GCC_PREFIX_HEADER = "Target Support Files/EAFeatureGuideView/EAFeatureGuideView-prefix.pch";
				IPHONEOS_DEPLOYMENT_TARGET = 7.0;
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PRIVATE_HEADERS_FOLDER_PATH = "";
				PRODUCT_MODULE_NAME = EAFeatureGuideView;
				PRODUCT_NAME = EAFeatureGuideView;
				PUBLIC_HEADERS_FOLDER_PATH = "";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Distribution;
		};
		4C28FCA29B9516DC3697EC1CFAC94C3F /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = ******************************** /* Pods-CameraBoxFR.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				ARCHS = "$(ARCHS_STANDARD)";
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				IPHONEOS_DEPLOYMENT_TARGET = 7.0;
				MACH_O_TYPE = staticlib;
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		4CCE7F679D5CB4EB4307E25FC7175D03 /* Distribution */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = ******************************** /* Pods-CameraBoxFR.distribution.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				ARCHS = "$(ARCHS_STANDARD)";
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				IPHONEOS_DEPLOYMENT_TARGET = 7.0;
				MACH_O_TYPE = staticlib;
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Distribution;
		};
		76F215AC5D39374D9BE234F6412690F3 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = ******************************** /* Pods-CameraBoxFR.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				ARCHS = "$(ARCHS_STANDARD)";
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				IPHONEOS_DEPLOYMENT_TARGET = 7.0;
				MACH_O_TYPE = staticlib;
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		8292EE84D677676DC19F2E261E216E30 /* Distribution */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_DISTRIBUTION=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 7.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Distribution;
		};
		B667E7AC964B28D3ACBD73792066A687 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B5C276EFEDA6BA39DD824FA7C8D2FDA4 /* EAFeatureGuideView.debug.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD)";
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				GCC_PREFIX_HEADER = "Target Support Files/EAFeatureGuideView/EAFeatureGuideView-prefix.pch";
				IPHONEOS_DEPLOYMENT_TARGET = 7.0;
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PRIVATE_HEADERS_FOLDER_PATH = "";
				PRODUCT_MODULE_NAME = EAFeatureGuideView;
				PRODUCT_NAME = EAFeatureGuideView;
				PUBLIC_HEADERS_FOLDER_PATH = "";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		C352469741B3A74ED19BE4F809ED9D7A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_DEBUG=1",
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 7.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Debug;
		};
		F26A457E3AE997331A7B7AA0E1DF884C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_RELEASE=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 7.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		0BD135C8BA76B006B3FDBF521A86CDB1 /* Build configuration list for PBXNativeTarget "EAFeatureGuideView" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B667E7AC964B28D3ACBD73792066A687 /* Debug */,
				4B82B08A62884B69ABA44C8F60B528E0 /* Distribution */,
				47DF1026E77EFB85739E6FF2A63EE8F5 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C352469741B3A74ED19BE4F809ED9D7A /* Debug */,
				8292EE84D677676DC19F2E261E216E30 /* Distribution */,
				F26A457E3AE997331A7B7AA0E1DF884C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A75DB5EE2883061787F80F3BF0AD71A5 /* Build configuration list for PBXNativeTarget "Pods-CameraBoxFR" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				4C28FCA29B9516DC3697EC1CFAC94C3F /* Debug */,
				4CCE7F679D5CB4EB4307E25FC7175D03 /* Distribution */,
				76F215AC5D39374D9BE234F6412690F3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = BFDFE7DC352907FC980B868725387E98 /* Project object */;
}
