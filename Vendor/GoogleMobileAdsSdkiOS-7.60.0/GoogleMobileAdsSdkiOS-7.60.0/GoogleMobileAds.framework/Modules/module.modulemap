framework module GoogleMobileAds {
  umbrella header "GoogleMobileAds.h"

  export *
  module * { export * }

  link "sqlite3"
  link "z"

  link framework "AdSupport"
  link framework "AudioToolbox"
  link framework "AVFoundation"
  link framework "CFNetwork"
  link framework "CoreGraphics"
  link framework "CoreMedia"
  link framework "CoreTelephony"
  link framework "CoreVideo"
  link framework "Foundation"
  link framework "JavaScriptCore"
  link framework "MediaPlayer"
  link framework "MessageUI"
  link framework "MobileCoreServices"
  link framework "QuartzCore"
  link framework "SafariServices"
  link framework "Security"
  link framework "StoreKit"
  link framework "SystemConfiguration"
  link framework "UIKit"
  link framework "WebKit"

  header "GoogleMobileAdsDefines.h"

  header "GADMobileAds.h"

  header "GADAdFormat.h"
  header "GADAdMetadata.h"
  header "GADAdNetworkExtras.h"
  header "GADAdSize.h"
  header "GADAdValue.h"
  header "GADAppOpenAd.h"
  header "GADAudioVideoManager.h"
  header "GADAudioVideoManagerDelegate.h"
  header "GADBannerView.h"
  header "GADBannerViewDelegate.h"
  header "GADDebugOptionsViewController.h"
  header "GADDisplayAdMeasurement.h"
  header "GADExtras.h"
  header "GADInAppPurchase.h"
  header "GADInAppPurchaseDelegate.h"
  header "GADInitializationStatus.h"
  header "GADInstreamAd.h"
  header "GADInstreamAdView.h"
  header "GADInterstitial.h"
  header "GADInterstitialDelegate.h"
  header "GADMediaAspectRatio.h"
  header "GADMediaContent.h"
  header "GADMediaView.h"
  header "GADNativeExpressAdView.h"
  header "GADNativeExpressAdViewDelegate.h"
  header "GADPresentationError.h"
  header "GADRequest.h"
  header "GADRequestConfiguration.h"
  header "GADRequestError.h"
  header "GADResponseInfo.h"
  header "GADVideoController.h"
  header "GADVideoControllerDelegate.h"
  header "GADVideoOptions.h"

  header "DFPBannerView.h"
  header "DFPBannerViewOptions.h"
  header "DFPCustomRenderedAd.h"
  header "DFPCustomRenderedBannerViewDelegate.h"
  header "DFPCustomRenderedInterstitialDelegate.h"
  header "DFPInterstitial.h"
  header "DFPRequest.h"
  header "GADAdSizeDelegate.h"
  header "GADAppEventDelegate.h"

  header "GADAdLoader.h"
  header "GADAdLoaderAdTypes.h"
  header "GADAdLoaderDelegate.h"

  header "GADAdChoicesView.h"
  header "GADNativeAd.h"
  header "GADNativeAdDelegate.h"
  header "GADNativeAdImage.h"
  header "GADNativeAdImage+Mediation.h"
  header "GADNativeCustomTemplateAd.h"
  header "GADUnifiedNativeAd.h"
  header "GADUnifiedNativeAd+ConfirmationClick.h"
  header "GADUnifiedNativeAd+CustomClickGesture.h"
  header "GADUnifiedNativeAdAssetIdentifiers.h"
  header "GADUnifiedNativeAdDelegate.h"
  header "GADUnifiedNativeAdUnconfirmedClickDelegate.h"

  header "GADDelayedAdRenderingOptions.h"
  header "GADMultipleAdsAdLoaderOptions.h"
  header "GADMuteThisAdReason.h"
  header "GADNativeAdImageAdLoaderOptions.h"
  header "GADNativeAdMediaAdLoaderOptions.h"
  header "GADNativeAdViewAdOptions.h"
  header "GADNativeMuteThisAdLoaderOptions.h"

  header "GADCustomEventBanner.h"
  header "GADCustomEventBannerDelegate.h"
  header "GADCustomEventExtras.h"
  header "GADCustomEventInterstitial.h"
  header "GADCustomEventInterstitialDelegate.h"
  header "GADCustomEventNativeAd.h"
  header "GADCustomEventNativeAdDelegate.h"
  header "GADCustomEventParameters.h"
  header "GADCustomEventRequest.h"

  header "GADDynamicHeightSearchRequest.h"
  header "GADSearchBannerView.h"

  header "GADAdReward.h"
  header "GADRewardBasedVideoAd.h"
  header "GADRewardBasedVideoAdDelegate.h"
  header "GADRewardedAd.h"
  header "GADRewardedAdDelegate.h"
  header "GADRewardedAdMetadataDelegate.h"
  header "GADServerSideVerificationOptions.h"

  header "Mediation/GADMAdNetworkAdapterProtocol.h"
  header "Mediation/GADMAdNetworkConnectorProtocol.h"
  header "Mediation/GADMediatedUnifiedNativeAd.h"
  header "Mediation/GADMediatedUnifiedNativeAdNotificationSource.h"
  header "Mediation/GADMediationAd.h"
  header "Mediation/GADMediationAdapter.h"
  header "Mediation/GADMediationAdConfiguration.h"
  header "Mediation/GADMediationAdEventDelegate.h"
  header "Mediation/GADMediationAdRequest.h"
  header "Mediation/GADMediationAdSize.h"
  header "Mediation/GADMediationBannerAd.h"
  header "Mediation/GADMediationInterstitialAd.h"
  header "Mediation/GADMediationNativeAd.h"
  header "Mediation/GADMediationRewardedAd.h"
  header "Mediation/GADMediationServerConfiguration.h"
  header "Mediation/GADMEnums.h"
  header "Mediation/GADMRewardBasedVideoAdNetworkAdapterProtocol.h"
  header "Mediation/GADMRewardBasedVideoAdNetworkConnectorProtocol.h"
  header "Mediation/GADVersionNumber.h"


  header "RTBMediation/GADRTBAdapter.h"
  header "RTBMediation/GADRTBRequestParameters.h"
}
