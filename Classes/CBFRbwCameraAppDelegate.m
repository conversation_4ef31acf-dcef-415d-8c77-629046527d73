#import "CBFRbwCameraAppDelegate.h"
#import "CBFRMainViewController.h"
@implementation CBFRbwCameraAppDelegate
@synthesize window;
@synthesize cbfr_mainViewController;
- (void)applicationDidFinishLaunching:(UIApplication *)application {
    [GADMobileAds.sharedInstance startWithCompletionHandler:nil];
#ifdef CBFR_KW_DEBUG
#if 1
    printf("[GADMobileAds versionNumber] = %s\n", GADGetStringFromVersionNumber(GADMobileAds.sharedInstance.versionNumber).cString);
#endif
#endif
	CBFRMainViewController *aController = [[CBFRMainViewController alloc] initWithNibName:@"CBFRMainView" bundle:nil];
	self.cbfr_mainViewController = aController;
#ifdef CBFR_KW_ADMOB_TEST
    GADMobileAds.sharedInstance.requestConfiguration.testDeviceIdentifiers = @[];
#endif
    window.rootViewController = aController;
    [window makeKeyAndVisible];
}
- (void)applicationDidBecomeActive:(UIApplication *)application
{
}
- (void)dealloc {
}
@end
