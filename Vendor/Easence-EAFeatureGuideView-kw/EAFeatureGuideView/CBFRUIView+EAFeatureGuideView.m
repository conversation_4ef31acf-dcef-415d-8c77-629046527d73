#import "CBFRUIView+EAFeatureGuideView.h"
typedef NS_ENUM(NSUInteger, EAFeatureItemLocation) {
    EAFeatureItemLocationDefault = 0,
    EAFeatureItemLocationUp = 1 << 1,
    EAFeatureItemLocationLeft = 1 << 2,
    EAFeatureItemLocationDown = 1 << 3,
    EAFeatureItemLocationRight = 1 << 4
};
@import ObjectiveC.runtime;
@implementation UIView (cbfr_EAFeatureGuideView)
- (void)cbfr_showWithFeatureItems:(NSArray<CBFREAFeatureItem *> *)featureItems saveKeyName:(NSString *)keyName inVersion:(NSString *)version
{
#ifdef KW_DEBUg
    NSLog(@"%s", __PRETTY_FUNCTION__);
#endif
    if([UIView cbfr_hasShowFeatureGuideWithKey:keyName version:version] || !self.window)
        return;
    [self cbfr_dismissFeatureGuideView];
    id observer = [self cbfr_getRotationOberserver];
    if(observer) {
        [[NSNotificationCenter defaultCenter] removeObserver:observer];
        [self setRotationOberserver:nil];
    }
    [self setRotationOberserver:[[NSNotificationCenter defaultCenter] addObserverForName:UIDeviceOrientationDidChangeNotification object:nil queue:nil usingBlock:^(NSNotification * _Nonnull note) {
#ifdef DEBUG
        NSLog(@"%s", __PRETTY_FUNCTION__);
#endif
        if (@available(ios 16.0, *)){
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [[self cbfr_getContainerView] removeFromSuperview];
                [self setContainerView:nil];
                [self cbfr_layoutSubviewsWithFeatureItems:featureItems];
            });
        }
        else
        {
            [self cbfr_dismissFeatureGuideView];
        }
    }]];
    [self cbfr_layoutSubviewsWithFeatureItems:featureItems];
    [self setKeyName:[NSString stringWithFormat:@"%@%@",keyName,version]];
}
- (void)cbfr_layoutSubviewsWithFeatureItems:(NSArray<CBFREAFeatureItem *> *)featureItems
{
    if(featureItems.count <= 0)
        return;
    UIView *containerView = [[UIView alloc] initWithFrame:self.window.bounds];
    containerView.tag = 10001;
    containerView.backgroundColor = [UIColor clearColor];
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(cbfr_touchedEvent:)];
    [containerView addGestureRecognizer:tap];
    [self setContainerView:containerView];
    [self.window addSubview :containerView];
    UIBezierPath *path = [UIBezierPath bezierPathWithRoundedRect:CGRectMake(0,0,self.window.bounds.size.width, self.window.bounds.size.height)cornerRadius:0];
    CAShapeLayer *shapeLayer = [CAShapeLayer layer];
    shapeLayer.path = path.CGPath;
    shapeLayer.fillRule = kCAFillRuleEvenOdd;
    shapeLayer.fillColor = [UIColor blackColor].CGColor;
    shapeLayer.opacity =0.8;
    [containerView.layer addSublayer:shapeLayer];
    NSMutableDictionary *actionDict = [NSMutableDictionary dictionary];
    [self setButtonActionsDictionary:actionDict];
    [featureItems enumerateObjectsUsingBlock:^(CBFREAFeatureItem * featureItem, NSUInteger idx, BOOL * _Nonnull stop) {
        actionDict[@(idx)] = [featureItem.action copy];
        [self cbfr_layoutWithFeatureItem:featureItem];
    }];
}
- (EAFeatureItemLocation)cbfr_getLocationForFeatureItem:(CBFREAFeatureItem *)featureItem
{
    EAFeatureItemLocation location = EAFeatureItemLocationDefault;
    CGRect frame = featureItem.cbfr_focusView ? [featureItem.cbfr_focusView convertRect:featureItem.cbfr_focusView.bounds toView:[self cbfr_getContainerView]] : featureItem.cbfr_focusRect;
    const NSInteger split = 16;
    CGFloat squareWidth = self.window.bounds.size.width / split;
    CGFloat squareHeight = self.window.bounds.size.height / split;
    CGFloat leftSpace = frame.origin.x;
    CGFloat rightSpace = self.window.bounds.size.width - (frame.origin.x + frame.size.width);
    CGFloat topSpace = frame.origin.y;
    CGFloat bottomSpace = self.window.bounds.size.height - (frame.origin.y + frame.size.height);
    if(frame.size.width <= squareWidth * (split - 1))
    {
        if((leftSpace - rightSpace) >= squareWidth)
        {
            location |= EAFeatureItemLocationRight;
        }
        else if((rightSpace - leftSpace) >= squareWidth)
        {
            location |= EAFeatureItemLocationLeft;
        }
    }
    if((topSpace - bottomSpace) > squareHeight)
    {
        location |= EAFeatureItemLocationDown;
    }
    else if((bottomSpace - topSpace) > squareHeight)
    {
        location |= EAFeatureItemLocationUp;
    }
    else if(featureItem.cbfr_introduceAlignmentPriority == EAFeatureItemAlignmentBottomFirst)
    {
        location |= EAFeatureItemLocationUp;
    }
    else
    {
        location |= EAFeatureItemLocationDown;
    }
    return location;
}
- (void)cbfr_layoutWithFeatureItem:(CBFREAFeatureItem *)featureItem
{
    UIView *containerView = [self cbfr_getContainerView];
    UIImageView *indicatorImageView = nil;
    UIView *introduceView = nil;
    UIButton *button = nil;
    CGRect featureItemFrame = featureItem.cbfr_focusView ? [featureItem.cbfr_focusView convertRect:featureItem.cbfr_focusView.bounds toView:containerView] : featureItem.cbfr_focusRect;
#ifdef DEBUG
    {
        NSLog(@"featureItem.cbfr_focusView.bounds = %@", NSStringFromCGRect(featureItem.cbfr_focusView.bounds));
        NSLog(@"featureItemFrame = %@", NSStringFromCGRect(featureItemFrame));
        NSLog(@"featureItem.cbfr_focusView = %@", featureItem.cbfr_focusView);
    }
#endif
    CAShapeLayer *shapeLayer = (CAShapeLayer *)[containerView.layer.sublayers firstObject];
    UIBezierPath *bezierPath = [UIBezierPath bezierPathWithCGPath:shapeLayer.path];
    featureItemFrame.origin.x += featureItem.cbfr_focusInsets.left;
    featureItemFrame.origin.y += featureItem.cbfr_focusInsets.top;
    featureItemFrame.size.width += featureItem.cbfr_focusInsets.right - featureItem.cbfr_focusInsets.left;
    featureItemFrame.size.height += featureItem.cbfr_focusInsets.bottom - featureItem.cbfr_focusInsets.top;
    [bezierPath appendPath:[UIBezierPath bezierPathWithRoundedRect:featureItemFrame cornerRadius:featureItem.cbfr_focusCornerRadius]];
    shapeLayer.path = bezierPath.CGPath;
    if(featureItem.action || featureItem.cbfr_introduce)
    {
        UIImage *indicatorImage = [UIImage imageNamed: @"cbfr_icon_ea_indicator"];
        CGSize imageSize = CGSizeMake(indicatorImage.size.width, indicatorImage.size.height);
        indicatorImageView = [[UIImageView alloc] initWithImage:indicatorImage];
        indicatorImageView.clipsToBounds = YES;
        indicatorImageView.contentMode = UIViewContentModeScaleAspectFit;
        indicatorImageView.frame = CGRectMake(0, 0, imageSize.width, imageSize.height);
        [containerView addSubview :indicatorImageView];
        if(featureItem.cbfr_introduce)
        {
            NSString *typeString = [[[featureItem.cbfr_introduce componentsSeparatedByString:@"."] lastObject] lowercaseString];
            if([typeString isEqualToString:@"png"] || [typeString isEqualToString:@"jpg"] || [typeString isEqualToString:@"jpeg"])
            {
                UIImage *introduceImage = [UIImage imageNamed:featureItem.cbfr_introduce];
                imageSize = CGSizeMake(introduceImage.size.width, introduceImage.size.height);
                UIImageView *imageView = [[UIImageView alloc] initWithImage:introduceImage];
                imageView.clipsToBounds = YES;
                imageView.contentMode = UIViewContentModeScaleAspectFit;
                imageView.frame = CGRectMake(0, 0, imageSize.width, imageSize.height);
                introduceView = imageView;
            }
            else
            {
                UILabel *introduceLabel = [[UILabel alloc] init];
                introduceLabel.backgroundColor = [UIColor clearColor];
                introduceLabel.numberOfLines = 0;
                introduceLabel.text = featureItem.cbfr_introduce;
                introduceLabel.font = featureItem.cbfr_introduceFont ?: [UIFont systemFontOfSize:13];
                introduceLabel.textColor = featureItem.cbfr_introduceTextColor ?: [UIColor whiteColor];
                introduceView = introduceLabel;
            }
            [containerView addSubview :introduceView];
        }
        if(featureItem.action || featureItem.cbfr_actionTitle)
        {
            button = [[UIButton alloc] init];
           UIImage * bg01 = [UIImage imageNamed: @"cbfr_icon_ea_background"];
            [button setBackgroundImage:[ bg01 resizableImageWithCapInsets:UIEdgeInsetsMake(4, 4, 4, 4)] forState:UIControlStateNormal];
            button.titleLabel.font = [UIFont systemFontOfSize:15];
            if(featureItem.cbfr_actionTitle.length <= 0)
            {
                featureItem.cbfr_actionTitle = @"知道了";
            }
            [button setTitle:featureItem.cbfr_actionTitle forState:UIControlStateNormal];
            [button sizeToFit];
            [button addTarget:self action:@selector(cbfr_buttonAction:) forControlEvents:UIControlEventTouchUpInside];
            CGRect frame = button.frame;
            frame.size.width += 20;
            frame.size.height += 10;
            button.frame = frame;
            [containerView addSubview :button];
        }
    }
    EAFeatureItemLocation location = [self cbfr_getLocationForFeatureItem:featureItem];
    CGRect introduceFrame = introduceView.frame;
    const CGFloat verticalSpacing = 10;
    if(location & EAFeatureItemLocationUp || location == EAFeatureItemLocationDefault)
    {
        indicatorImageView.layer.anchorPoint = CGPointMake(.5f, 0);
        indicatorImageView.center = CGPointMake(CGRectGetMinX(featureItemFrame) + CGRectGetWidth(featureItemFrame) / 2, CGRectGetMinY(featureItemFrame) + CGRectGetHeight(featureItemFrame) + verticalSpacing);
        if(location & EAFeatureItemLocationLeft)
        {
            CGAffineTransform transform = indicatorImageView.transform;
            indicatorImageView.transform = CGAffineTransformRotate(transform, - M_PI / 4);
            if([introduceView isKindOfClass:[UIImageView class]])
            {
                introduceFrame.origin.x = indicatorImageView.frame.origin.x;
                introduceFrame.origin.y = CGRectGetMaxY(indicatorImageView.frame) + verticalSpacing;
                introduceView.frame = introduceFrame;
            }
            else if([introduceView isKindOfClass:[UILabel class]])
            {
                CGRect rect = [featureItem.cbfr_introduce boundingRectWithSize:CGSizeMake(containerView.bounds.size.width - indicatorImageView.frame.origin.x * 2, CGFLOAT_MAX) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName: ((UILabel *)introduceView).font} context:nil];
                introduceView.frame = CGRectMake(indicatorImageView.frame.origin.x, CGRectGetMaxY(indicatorImageView.frame) + verticalSpacing, rect.size.width, rect.size.height);
            }
            if(introduceView.frame.size.width < indicatorImageView.frame.size.width)
            {
                CGPoint center = introduceView.center;
                center.x = indicatorImageView.frame.origin.x + indicatorImageView.frame.size.width;
                introduceView.center = center;
            }
        }
        else if(location & EAFeatureItemLocationRight)
        {
            CGAffineTransform transform = indicatorImageView.transform;
            indicatorImageView.transform = CGAffineTransformRotate(transform,M_PI / 4);
            if([introduceView isKindOfClass:[UIImageView class]])
            {
                introduceFrame.origin.x = indicatorImageView.frame.origin.x + indicatorImageView.frame.size.width - introduceFrame.size.width;
                introduceFrame.origin.y = CGRectGetMaxY(indicatorImageView.frame) + verticalSpacing;
                introduceView.frame = introduceFrame;
            }
            else if([introduceView isKindOfClass:[UILabel class]])
            {
                CGRect rect = [featureItem.cbfr_introduce boundingRectWithSize:CGSizeMake( containerView.bounds.size.width - (containerView.bounds.size.width - indicatorImageView.frame.origin.x - indicatorImageView.frame.size.width) * 2, CGFLOAT_MAX) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName: ((UILabel *)introduceView).font} context:nil];
                introduceView.frame = CGRectMake(indicatorImageView.frame.origin.x + indicatorImageView.frame.size.width - rect.size.width, CGRectGetMaxY(indicatorImageView.frame) + verticalSpacing, rect.size.width, rect.size.height);
            }
            if(introduceView.frame.size.width < indicatorImageView.frame.size.width)
            {
                CGPoint center = introduceView.center;
                center.x = indicatorImageView.frame.origin.x;
                introduceView.center = center;
            }
        }
        else 
        {
            if([introduceView isKindOfClass:[UIImageView class]])
            {
                introduceView.center = CGPointMake(indicatorImageView.center.x, CGRectGetMaxY(indicatorImageView.frame) + verticalSpacing + introduceFrame.size.height / 2);
            }
            else if([introduceView isKindOfClass:[UILabel class]])
            {
                UILabel *label = (UILabel *)introduceView;
                label.textAlignment = NSTextAlignmentCenter;
                CGRect rect = [featureItem.cbfr_introduce boundingRectWithSize:CGSizeMake(containerView.bounds.size.width * 3 / 4, CGFLOAT_MAX) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName: ((UILabel *)introduceView).font} context:nil];
                introduceView.frame = CGRectMake((containerView.bounds.size.width - rect.size.width) / 2, CGRectGetMaxY(indicatorImageView.frame) + verticalSpacing, rect.size.width, rect.size.height);
            }
        }
    }
    else if(location & EAFeatureItemLocationDown)
    {
        CGFloat buttonVerticalOccupySpace = button ? CGRectGetHeight(button.frame) + verticalSpacing : 0;
        if(location & EAFeatureItemLocationLeft)
        {
            indicatorImageView.layer.anchorPoint = CGPointMake(.5f, 1.f);
            indicatorImageView.center = CGPointMake(CGRectGetMinX(featureItemFrame) + CGRectGetWidth(featureItemFrame) / 2, CGRectGetMinY(featureItemFrame) - CGRectGetHeight(indicatorImageView.frame));
            CGAffineTransform transform = indicatorImageView.transform;
            transform = CGAffineTransformTranslate(transform, CGRectGetHeight(indicatorImageView.frame) * sinf(M_PI / 4), 0);
            indicatorImageView.transform = CGAffineTransformRotate(transform,  - M_PI * 3 / 4);
            if([introduceView isKindOfClass:[UIImageView class]])
            {
                introduceFrame.origin.x = indicatorImageView.frame.origin.x;
                introduceFrame.origin.y = CGRectGetMinY(indicatorImageView.frame) - verticalSpacing - buttonVerticalOccupySpace - CGRectGetHeight(introduceView.frame);
                introduceView.frame = introduceFrame;
            }
            else if([introduceView isKindOfClass:[UILabel class]])
            {
                CGRect rect = [featureItem.cbfr_introduce boundingRectWithSize:CGSizeMake(containerView.bounds.size.width - indicatorImageView.frame.origin.x * 2, CGFLOAT_MAX) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName: ((UILabel *)introduceView).font} context:nil];
                introduceView.frame = CGRectMake(indicatorImageView.frame.origin.x, CGRectGetMinY(indicatorImageView.frame) - verticalSpacing - buttonVerticalOccupySpace - rect.size.height, rect.size.width, rect.size.height);
            }
            if(introduceView.frame.size.width < indicatorImageView.frame.size.width)
            {
                CGPoint center = introduceView.center;
                center.x = indicatorImageView.frame.origin.x + indicatorImageView.frame.size.width;
                introduceView.center = center;
            }
        }
        else if(location & EAFeatureItemLocationRight)
        {
            indicatorImageView.layer.anchorPoint = CGPointMake(.5f, 1.f);
            indicatorImageView.center = CGPointMake(CGRectGetMinX(featureItemFrame) + CGRectGetWidth(featureItemFrame) / 2, CGRectGetMinY(featureItemFrame) - CGRectGetHeight(indicatorImageView.frame));
            CGAffineTransform transform = indicatorImageView.transform;
            transform = CGAffineTransformTranslate(transform, - CGRectGetHeight(indicatorImageView.frame) * sinf(M_PI / 4), 0);
            indicatorImageView.transform = CGAffineTransformRotate(transform, M_PI * 3 / 4);
            if([introduceView isKindOfClass:[UIImageView class]])
            {
                introduceFrame.origin.x = indicatorImageView.frame.origin.x + indicatorImageView.frame.size.width - introduceFrame.size.width;
                introduceFrame.origin.y = CGRectGetMinY(indicatorImageView.frame) - verticalSpacing - buttonVerticalOccupySpace - CGRectGetHeight(introduceView.frame);
                introduceView.frame = introduceFrame;
            }
            else if([introduceView isKindOfClass:[UILabel class]])
            {
                CGRect rect = [featureItem.cbfr_introduce boundingRectWithSize:CGSizeMake(containerView.bounds.size.width - (containerView.bounds.size.width - indicatorImageView.frame.origin.x - indicatorImageView.frame.size.width) * 2, CGFLOAT_MAX) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName: ((UILabel *)introduceView).font} context:nil];
                introduceView.frame = CGRectMake(indicatorImageView.frame.origin.x + indicatorImageView.frame.size.width - rect.size.width, CGRectGetMinY(indicatorImageView.frame) - verticalSpacing - buttonVerticalOccupySpace - rect.size.height, rect.size.width, rect.size.height);
            }
            if(introduceView.frame.size.width < indicatorImageView.frame.size.width)
            {
                CGPoint center = introduceView.center;
                center.x = indicatorImageView.frame.origin.x;
                introduceView.center = center;
            }
        }
        else 
        {
            indicatorImageView.center = CGPointMake(CGRectGetMinX(featureItemFrame) + CGRectGetWidth(featureItemFrame) / 2, CGRectGetMinY(featureItemFrame) - verticalSpacing - CGRectGetHeight(indicatorImageView.bounds) / 2);
            CGAffineTransform transform = indicatorImageView.transform;
            indicatorImageView.transform = CGAffineTransformRotate(transform, M_PI);
            if([introduceView isKindOfClass:[UIImageView class]])
            {
                introduceView.center = CGPointMake(indicatorImageView.center.x, CGRectGetMinY(indicatorImageView.frame) - buttonVerticalOccupySpace - verticalSpacing - introduceFrame.size.height / 2);
            }
            else if([introduceView isKindOfClass:[UILabel class]])
            {
                UILabel *label = (UILabel *)introduceView;
                label.textAlignment = NSTextAlignmentCenter;
                CGRect rect = [featureItem.cbfr_introduce boundingRectWithSize:CGSizeMake(containerView.bounds.size.width * 3 / 4, CGFLOAT_MAX) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName: ((UILabel *)introduceView).font} context:nil];
                introduceView.frame = CGRectMake((containerView.bounds.size.width - rect.size.width) / 2, CGRectGetMinY(indicatorImageView.frame)  - buttonVerticalOccupySpace - verticalSpacing - rect.size.height, rect.size.width, rect.size.height);
            }
        }
    }
    button.center = CGPointMake(introduceView.center.x, CGRectGetMaxY(introduceView.frame) + verticalSpacing + button.frame.size.height / 2);
}
- (void)cbfr_buttonAction:(UIButton *) sender
{
    NSMutableDictionary *actionDict = [self cbfr_getButtonActionsDictionary];
    void (^action)(id sendr)  = actionDict[@(sender.tag)];
    if(action)
    {
        action(sender);
    }
    [self cbfr_dismissFeatureGuideView];
}
- (void)cbfr_dismissFeatureGuideView
{
    id observer = [self cbfr_getRotationOberserver];
    if(observer) {
        [[NSNotificationCenter defaultCenter] removeObserver:observer];
        [self setRotationOberserver:nil];
    }
    if(![self cbfr_getContainerView])
        return;
    [UIView setShowStatusWithKey:[self cbfr_getKeyName] status:YES];
    [[self cbfr_getContainerView] removeFromSuperview];
    [self setContainerView:nil];
}
- (void)cbfr_touchedEvent:(UITapGestureRecognizer *)tap
{
    if (tap.state == UIGestureRecognizerStateEnded)
    {
        [self cbfr_dismissFeatureGuideView];
    }
}
+ (BOOL)cbfr_hasShowFeatureGuideWithKey:(NSString *)keyName version:(NSString *)version
{
    if(!keyName)
        return NO;
    if(![version isEqualToString:[[NSBundle mainBundle] infoDictionary][@"CFBundleShortVersionString"]] && version)
    {
        return YES;
    }
    id result = [[NSUserDefaults standardUserDefaults] objectForKey:[NSString stringWithFormat:@"%@%@",keyName,version]];
    if(result)
    {
        return [result boolValue];
    }
    return NO;
}
+ (void)setShowStatusWithKey:(NSString *)keyName status:(BOOL) hasShow
{
    if(!keyName)
        return;
    [[NSUserDefaults standardUserDefaults] setBool:hasShow forKey:keyName];
    [[NSUserDefaults standardUserDefaults] synchronize];
}
- (void)setKeyName:(NSString *)keyName
{
    objc_setAssociatedObject(self, @selector(cbfr_getKeyName), keyName, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}
- (NSString *)cbfr_getKeyName
{
    return objc_getAssociatedObject(self, _cmd);
}
- (void)setContainerView:(UIView *)containerView
{
    objc_setAssociatedObject(self, @selector(cbfr_getContainerView), containerView, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}
- (UIView *)cbfr_getContainerView
{
    return objc_getAssociatedObject(self, _cmd);
}
- (void)setButtonActionsDictionary:(NSMutableDictionary *)actionsDictionary
{
    objc_setAssociatedObject(self, @selector(cbfr_getButtonActionsDictionary), actionsDictionary, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}
- (NSMutableDictionary *)cbfr_getButtonActionsDictionary
{
    return objc_getAssociatedObject(self, _cmd);
}
- (id)cbfr_getRotationOberserver {
    return objc_getAssociatedObject(self, _cmd);
}
- (void)setRotationOberserver:(id)rotationOberserver
{
    objc_setAssociatedObject(self, @selector(cbfr_getRotationOberserver), rotationOberserver, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}
@end
