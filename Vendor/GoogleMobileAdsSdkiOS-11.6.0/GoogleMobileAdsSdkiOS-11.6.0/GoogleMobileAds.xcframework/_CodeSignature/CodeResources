<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>ios-arm64/GoogleMobileAds.framework/GoogleMobileAds</key>
		<data>
		EL/oX0qz/i7P9efZGIlrz/I2rvc=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADAdChoicesPosition.h</key>
		<data>
		3HUQ7uVcN3WWKscbUgvRTYb6zGU=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADAdChoicesView.h</key>
		<data>
		qNnvxOY2HmWZxgGuaI6NYKXEFr0=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADAdFormat.h</key>
		<data>
		xra0fPg7SwSG7qdLOTU+/Z2/Gzg=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADAdLoader.h</key>
		<data>
		BchePlG8Q1pZwdU4NuK/LPG6BSs=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADAdLoaderAdTypes.h</key>
		<data>
		8X8NXKflWuqUW1ZX7DWYTZ1sEJk=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADAdLoaderDelegate.h</key>
		<data>
		jsfTiQpvxV8kM+FnNaKpxmMWiJU=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADAdMetadata.h</key>
		<data>
		R+mmMd9BciU+g2LByLSmev5MKeo=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADAdNetworkExtras.h</key>
		<data>
		pEhVRx0KAS7ZddDzUBDRkaExh8I=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADAdReward.h</key>
		<data>
		jtoiYod9jZUtgHzWgVX6a6E9+ZM=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADAdSize.h</key>
		<data>
		uNdf9JCf5I4jdzYwjD7d9tTYjmo=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADAdSizeDelegate.h</key>
		<data>
		zsFshiKVtlod87kG3OVd6NUMOxg=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADAdValue.h</key>
		<data>
		nkgwpuGaZUS48lKEpALhJFnmgGY=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADAppEventDelegate.h</key>
		<data>
		TTXmWnlK+BB7uhMTsV48MDHpkI8=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADAppOpenAd.h</key>
		<data>
		Pi+nGzqErKJRbC32MObNfgqE+QQ=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADAudioVideoManager.h</key>
		<data>
		3lS8xsfXwX4ZR6auPLRSpvFrQqQ=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADAudioVideoManagerDelegate.h</key>
		<data>
		ZJvHvp3bCw4hRMgzzHqJDvM3HyY=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADBannerView.h</key>
		<data>
		dQNnVaOV8vNUGMd/3nGfq1G19e0=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADBannerViewDelegate.h</key>
		<data>
		Qrfo7A4zl/bDXNLF7xzuNk/x3+0=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADCustomEventBanner.h</key>
		<data>
		qGCy1A+noahdh79cgrK37Dk1L1E=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADCustomEventBannerDelegate.h</key>
		<data>
		yeVqeb6lj6vnwM+SiT6O4S6UfD0=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADCustomEventExtras.h</key>
		<data>
		tyDEfUrnsRXNoYZWtaImmnCDFCk=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADCustomEventInterstitial.h</key>
		<data>
		l8ZlvOyDlckL0QfqyVbVYPlaNsM=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADCustomEventInterstitialDelegate.h</key>
		<data>
		7ztiyvehcDtHg05srSRIrrztG9o=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADCustomEventNativeAd.h</key>
		<data>
		M+VlNgp+AUY2mtcxPZ4X/PKpiwc=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADCustomEventNativeAdDelegate.h</key>
		<data>
		8lyeX5v56tQ/QKVA546BH9eHvTg=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADCustomEventParameters.h</key>
		<data>
		yR6LJNNnOKOLwfRXiLg6xcvILCU=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADCustomEventRequest.h</key>
		<data>
		6LUAOkcdSUtdhOYFQA92wR5aXs0=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADCustomNativeAd.h</key>
		<data>
		tn/GIQH6dk1LLXEU91hDaNPg3Qk=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADCustomNativeAdDelegate.h</key>
		<data>
		T3q4J5qaE+fdMt3285vpz2ApyQU=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADDebugOptionsViewController.h</key>
		<data>
		zLjSO2X3zWKSAjU2zL7c1jz4Piw=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADDisplayAdMeasurement.h</key>
		<data>
		lBOi88QFmY681Nn+rpiQ3AyXmhE=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADDynamicHeightSearchRequest.h</key>
		<data>
		ZaF68/opQBrmdxRVGBcjPoilX7I=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADExtras.h</key>
		<data>
		yYeHP3vuzvjcGhxtTTJ4SWluWdk=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADFullScreenContentDelegate.h</key>
		<data>
		+/443o8MPR5e20F7QuE+IUiIyTc=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADInitializationStatus.h</key>
		<data>
		aDPuW2qEWx4vFqXC8J7su8SS1mw=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADInterstitialAd.h</key>
		<data>
		KJTYfV6Ww8kJCuFQ3jSl7FBDHCk=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADMediaAspectRatio.h</key>
		<data>
		MzhWPfGm7SG3MHf0XRxd/zOTGK4=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADMediaContent.h</key>
		<data>
		wYEddwgajDVbX61ks28RnXWgMcc=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADMediaView.h</key>
		<data>
		TyAN6KtLY2jqOQ/RfcyBfZy6Mak=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADMobileAds.h</key>
		<data>
		IhlvvJHtNFqzRuU86CO7rUFNBpQ=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADMultipleAdsAdLoaderOptions.h</key>
		<data>
		pmnk3VeQeOcbL0PW0hCKfx0MxWc=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADMuteThisAdReason.h</key>
		<data>
		5WYeY3zyUAffO7yxDRKUW56PUEE=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADNativeAd+ConfirmationClick.h</key>
		<data>
		tLFTWRd19SRrx9pso51Q//uUP9c=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADNativeAd+CustomClickGesture.h</key>
		<data>
		SSL/Qe9KWkvYHc7tqXSz8iez+9k=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADNativeAd.h</key>
		<data>
		Q57TQPri0v/hKe8djYpgSUPCYV4=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADNativeAdAssetIdentifiers.h</key>
		<data>
		WF8V8qBuQYzYube1bpjhk6VTtQc=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADNativeAdCustomClickGestureOptions.h</key>
		<data>
		zbYVgF8KaoqwycbD78tgtNGzbiY=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADNativeAdDelegate.h</key>
		<data>
		TS5G14xkP4NoNjiQrUoNBiPC+Qo=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADNativeAdImage+Mediation.h</key>
		<data>
		jERrfqGRcxU8DeM6GzjxB9eP1gQ=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADNativeAdImage.h</key>
		<data>
		PItp6/8tb/SyIyliABTwTWso6JQ=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADNativeAdImageAdLoaderOptions.h</key>
		<data>
		V+yNFdMJiUaCnKf9t6JhlWj/MEM=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADNativeAdMediaAdLoaderOptions.h</key>
		<data>
		tQY9a/XhPOV7Tkg3xjrF9iucd0Y=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADNativeAdUnconfirmedClickDelegate.h</key>
		<data>
		adFUhMad6x1Nszo7jnx/6Go5M64=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADNativeAdViewAdOptions.h</key>
		<data>
		3qrqu7IZca/R79fLGIOj5kP74pM=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADNativeMuteThisAdLoaderOptions.h</key>
		<data>
		OMACP/+e4wOxWCxjCBqfbbXvSn8=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADPresentationError.h</key>
		<data>
		/DwQgJA26rp29qAElahS4M92Ves=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADQueryInfo.h</key>
		<data>
		bbx8+sTRZZ18wKFKbdosCh19LP8=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADRequest.h</key>
		<data>
		fA4dVT+ci8eDJOLoX1W56No8Wj8=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADRequestConfiguration.h</key>
		<data>
		p8xu90L6d/+5xIYqcUedMNMAOas=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADRequestError.h</key>
		<data>
		polhNCpFJ9FdSyv9ZH+r8s5tVWA=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADResponseInfo.h</key>
		<data>
		nfuBQCjj9cbnaBkHtWmL9khecY4=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADRewardedAd.h</key>
		<data>
		pAkVn1528dLsfgHFDtWtN97HS88=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADRewardedInterstitialAd.h</key>
		<data>
		I8l6Sm2AP2znCt+WvYH3rUCNZc4=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADSearchBannerView.h</key>
		<data>
		eatzmubDJRMpScDkvBu3MdRUCRc=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADServerSideVerificationOptions.h</key>
		<data>
		rPJOQpfl792Rau0cVjW0tIF0QoM=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADVideoController.h</key>
		<data>
		ROr6GuDE2MFIjzE8K+EqjJIongA=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADVideoControllerDelegate.h</key>
		<data>
		RPSqq2oNrkWsBiC944aUTYbsGdY=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADVideoOptions.h</key>
		<data>
		4gKbOjeYKPDI8y46f7MYmujVolE=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GAMBannerView.h</key>
		<data>
		USGsC8COUC7FEu2FzyjpIu9twkk=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GAMBannerViewOptions.h</key>
		<data>
		UM7nuKLTtZRWtQFxH3g31LWzMUI=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GAMInterstitialAd.h</key>
		<data>
		Z09wFz3tGqvcJI4/M5Ypp8FFlmI=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GAMRequest.h</key>
		<data>
		PSRXhPJJoqWwh9qC5jGkxT1yU4Q=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GoogleMobileAds.h</key>
		<data>
		ZvHPNB+vixckrnfiuMGTFD0Qdic=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GoogleMobileAdsDefines.h</key>
		<data>
		ajvTC+Y3CV+fidHio4vx7QWS/6M=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/Mediation/GADMAdNetworkAdapterProtocol.h</key>
		<data>
		m5SQPMUYo8Qedy/ZtOBil8J613s=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/Mediation/GADMAdNetworkConnectorProtocol.h</key>
		<data>
		5fnIS7Tr7E/R0kXpE2Z74e2uGB4=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/Mediation/GADMEnums.h</key>
		<data>
		tJvLWOlUGS/T88+TLEUQKMlOr00=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/Mediation/GADMediatedUnifiedNativeAd.h</key>
		<data>
		/zQcy15VvaZwUYK9vbBuQt8kYMw=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/Mediation/GADMediatedUnifiedNativeAdNotificationSource.h</key>
		<data>
		sRut+yR1ITR2iMoSkRbm452gWnU=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/Mediation/GADMediationAd.h</key>
		<data>
		6EqmzUEtxFunPoFRSaTvHWmwM2g=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/Mediation/GADMediationAdConfiguration.h</key>
		<data>
		z4dRgZl3oSWtJElWBITmwIiAP4w=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/Mediation/GADMediationAdEventDelegate.h</key>
		<data>
		ZqBdeT5cUaS3twsIUjhbpoVlfZE=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/Mediation/GADMediationAdRequest.h</key>
		<data>
		yMjoc9zFKLdSN8U4y1Ew/9c+Qok=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/Mediation/GADMediationAdSize.h</key>
		<data>
		fSUT1kpL5tJuVulCa/ppszvpHe4=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/Mediation/GADMediationAdapter.h</key>
		<data>
		qbJC3o87Nj4opncueh1M2ynCVSg=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/Mediation/GADMediationAppOpenAd.h</key>
		<data>
		DW1HyY7MiRIkXeYLeHZZyJ0hC1U=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/Mediation/GADMediationBannerAd.h</key>
		<data>
		fRuNpE7SjmnAIg8AtxYdcnXlecg=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/Mediation/GADMediationInterstitialAd.h</key>
		<data>
		c7z+4eDsplD/cP+E4R8opNB8q5s=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/Mediation/GADMediationNativeAd.h</key>
		<data>
		Xx7cfccdAUaHeaH9jxLR5YcHnH4=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/Mediation/GADMediationRewardedAd.h</key>
		<data>
		wri4Lk1jBA3XDAFRq19dEh8IsJU=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/Mediation/GADMediationServerConfiguration.h</key>
		<data>
		A54+22v1zABH2pYZw6R1f3iADyA=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/Mediation/GADVersionNumber.h</key>
		<data>
		Q0WWOP9O1Wi+UuOPkTHeSrvHpCU=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/QueryInfo/GADRequest+AdString.h</key>
		<data>
		vcKuPzFb6BRvsJTPBvvY1RrJfds=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/RTBMediation/GADRTBAdapter.h</key>
		<data>
		FQ+EY0l9vQ1iuM0vClDfyG6U5cg=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/RTBMediation/GADRTBRequestParameters.h</key>
		<data>
		IHJsVQZTqqjDy32s9qMZwkytcAY=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Info.plist</key>
		<data>
		f/aILqtfmZaT4TvvRQj6GXwHA2s=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/Modules/module.modulemap</key>
		<data>
		lfIY8btA05hFyBU2chqKw5NhZLs=
		</data>
		<key>ios-arm64/GoogleMobileAds.framework/PrivacyInfo.xcprivacy</key>
		<data>
		VWCA3MeaiPMoOAS1uOteTSLX8js=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/GoogleMobileAds</key>
		<data>
		AEzIK7jnYZ9hZfeV5Zo6O+KYlRs=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADAdChoicesPosition.h</key>
		<data>
		3HUQ7uVcN3WWKscbUgvRTYb6zGU=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADAdChoicesView.h</key>
		<data>
		qNnvxOY2HmWZxgGuaI6NYKXEFr0=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADAdFormat.h</key>
		<data>
		xra0fPg7SwSG7qdLOTU+/Z2/Gzg=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADAdLoader.h</key>
		<data>
		BchePlG8Q1pZwdU4NuK/LPG6BSs=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADAdLoaderAdTypes.h</key>
		<data>
		8X8NXKflWuqUW1ZX7DWYTZ1sEJk=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADAdLoaderDelegate.h</key>
		<data>
		jsfTiQpvxV8kM+FnNaKpxmMWiJU=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADAdMetadata.h</key>
		<data>
		R+mmMd9BciU+g2LByLSmev5MKeo=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADAdNetworkExtras.h</key>
		<data>
		pEhVRx0KAS7ZddDzUBDRkaExh8I=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADAdReward.h</key>
		<data>
		jtoiYod9jZUtgHzWgVX6a6E9+ZM=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADAdSize.h</key>
		<data>
		uNdf9JCf5I4jdzYwjD7d9tTYjmo=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADAdSizeDelegate.h</key>
		<data>
		zsFshiKVtlod87kG3OVd6NUMOxg=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADAdValue.h</key>
		<data>
		nkgwpuGaZUS48lKEpALhJFnmgGY=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADAppEventDelegate.h</key>
		<data>
		TTXmWnlK+BB7uhMTsV48MDHpkI8=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADAppOpenAd.h</key>
		<data>
		Pi+nGzqErKJRbC32MObNfgqE+QQ=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADAudioVideoManager.h</key>
		<data>
		3lS8xsfXwX4ZR6auPLRSpvFrQqQ=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADAudioVideoManagerDelegate.h</key>
		<data>
		ZJvHvp3bCw4hRMgzzHqJDvM3HyY=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADBannerView.h</key>
		<data>
		dQNnVaOV8vNUGMd/3nGfq1G19e0=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADBannerViewDelegate.h</key>
		<data>
		Qrfo7A4zl/bDXNLF7xzuNk/x3+0=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADCustomEventBanner.h</key>
		<data>
		qGCy1A+noahdh79cgrK37Dk1L1E=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADCustomEventBannerDelegate.h</key>
		<data>
		yeVqeb6lj6vnwM+SiT6O4S6UfD0=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADCustomEventExtras.h</key>
		<data>
		tyDEfUrnsRXNoYZWtaImmnCDFCk=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADCustomEventInterstitial.h</key>
		<data>
		l8ZlvOyDlckL0QfqyVbVYPlaNsM=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADCustomEventInterstitialDelegate.h</key>
		<data>
		7ztiyvehcDtHg05srSRIrrztG9o=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADCustomEventNativeAd.h</key>
		<data>
		M+VlNgp+AUY2mtcxPZ4X/PKpiwc=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADCustomEventNativeAdDelegate.h</key>
		<data>
		8lyeX5v56tQ/QKVA546BH9eHvTg=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADCustomEventParameters.h</key>
		<data>
		yR6LJNNnOKOLwfRXiLg6xcvILCU=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADCustomEventRequest.h</key>
		<data>
		6LUAOkcdSUtdhOYFQA92wR5aXs0=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADCustomNativeAd.h</key>
		<data>
		tn/GIQH6dk1LLXEU91hDaNPg3Qk=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADCustomNativeAdDelegate.h</key>
		<data>
		T3q4J5qaE+fdMt3285vpz2ApyQU=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADDebugOptionsViewController.h</key>
		<data>
		zLjSO2X3zWKSAjU2zL7c1jz4Piw=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADDisplayAdMeasurement.h</key>
		<data>
		lBOi88QFmY681Nn+rpiQ3AyXmhE=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADDynamicHeightSearchRequest.h</key>
		<data>
		ZaF68/opQBrmdxRVGBcjPoilX7I=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADExtras.h</key>
		<data>
		yYeHP3vuzvjcGhxtTTJ4SWluWdk=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADFullScreenContentDelegate.h</key>
		<data>
		+/443o8MPR5e20F7QuE+IUiIyTc=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADInitializationStatus.h</key>
		<data>
		aDPuW2qEWx4vFqXC8J7su8SS1mw=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADInterstitialAd.h</key>
		<data>
		KJTYfV6Ww8kJCuFQ3jSl7FBDHCk=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADMediaAspectRatio.h</key>
		<data>
		MzhWPfGm7SG3MHf0XRxd/zOTGK4=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADMediaContent.h</key>
		<data>
		wYEddwgajDVbX61ks28RnXWgMcc=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADMediaView.h</key>
		<data>
		TyAN6KtLY2jqOQ/RfcyBfZy6Mak=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADMobileAds.h</key>
		<data>
		IhlvvJHtNFqzRuU86CO7rUFNBpQ=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADMultipleAdsAdLoaderOptions.h</key>
		<data>
		pmnk3VeQeOcbL0PW0hCKfx0MxWc=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADMuteThisAdReason.h</key>
		<data>
		5WYeY3zyUAffO7yxDRKUW56PUEE=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADNativeAd+ConfirmationClick.h</key>
		<data>
		tLFTWRd19SRrx9pso51Q//uUP9c=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADNativeAd+CustomClickGesture.h</key>
		<data>
		SSL/Qe9KWkvYHc7tqXSz8iez+9k=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADNativeAd.h</key>
		<data>
		Q57TQPri0v/hKe8djYpgSUPCYV4=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADNativeAdAssetIdentifiers.h</key>
		<data>
		WF8V8qBuQYzYube1bpjhk6VTtQc=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADNativeAdCustomClickGestureOptions.h</key>
		<data>
		zbYVgF8KaoqwycbD78tgtNGzbiY=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADNativeAdDelegate.h</key>
		<data>
		TS5G14xkP4NoNjiQrUoNBiPC+Qo=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADNativeAdImage+Mediation.h</key>
		<data>
		jERrfqGRcxU8DeM6GzjxB9eP1gQ=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADNativeAdImage.h</key>
		<data>
		PItp6/8tb/SyIyliABTwTWso6JQ=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADNativeAdImageAdLoaderOptions.h</key>
		<data>
		V+yNFdMJiUaCnKf9t6JhlWj/MEM=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADNativeAdMediaAdLoaderOptions.h</key>
		<data>
		tQY9a/XhPOV7Tkg3xjrF9iucd0Y=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADNativeAdUnconfirmedClickDelegate.h</key>
		<data>
		adFUhMad6x1Nszo7jnx/6Go5M64=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADNativeAdViewAdOptions.h</key>
		<data>
		3qrqu7IZca/R79fLGIOj5kP74pM=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADNativeMuteThisAdLoaderOptions.h</key>
		<data>
		OMACP/+e4wOxWCxjCBqfbbXvSn8=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADPresentationError.h</key>
		<data>
		/DwQgJA26rp29qAElahS4M92Ves=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADQueryInfo.h</key>
		<data>
		bbx8+sTRZZ18wKFKbdosCh19LP8=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADRequest.h</key>
		<data>
		fA4dVT+ci8eDJOLoX1W56No8Wj8=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADRequestConfiguration.h</key>
		<data>
		p8xu90L6d/+5xIYqcUedMNMAOas=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADRequestError.h</key>
		<data>
		polhNCpFJ9FdSyv9ZH+r8s5tVWA=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADResponseInfo.h</key>
		<data>
		nfuBQCjj9cbnaBkHtWmL9khecY4=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADRewardedAd.h</key>
		<data>
		pAkVn1528dLsfgHFDtWtN97HS88=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADRewardedInterstitialAd.h</key>
		<data>
		I8l6Sm2AP2znCt+WvYH3rUCNZc4=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADSearchBannerView.h</key>
		<data>
		eatzmubDJRMpScDkvBu3MdRUCRc=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADServerSideVerificationOptions.h</key>
		<data>
		rPJOQpfl792Rau0cVjW0tIF0QoM=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADVideoController.h</key>
		<data>
		ROr6GuDE2MFIjzE8K+EqjJIongA=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADVideoControllerDelegate.h</key>
		<data>
		RPSqq2oNrkWsBiC944aUTYbsGdY=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADVideoOptions.h</key>
		<data>
		4gKbOjeYKPDI8y46f7MYmujVolE=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GAMBannerView.h</key>
		<data>
		USGsC8COUC7FEu2FzyjpIu9twkk=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GAMBannerViewOptions.h</key>
		<data>
		UM7nuKLTtZRWtQFxH3g31LWzMUI=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GAMInterstitialAd.h</key>
		<data>
		Z09wFz3tGqvcJI4/M5Ypp8FFlmI=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GAMRequest.h</key>
		<data>
		PSRXhPJJoqWwh9qC5jGkxT1yU4Q=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GoogleMobileAds.h</key>
		<data>
		ZvHPNB+vixckrnfiuMGTFD0Qdic=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GoogleMobileAdsDefines.h</key>
		<data>
		ajvTC+Y3CV+fidHio4vx7QWS/6M=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/Mediation/GADMAdNetworkAdapterProtocol.h</key>
		<data>
		m5SQPMUYo8Qedy/ZtOBil8J613s=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/Mediation/GADMAdNetworkConnectorProtocol.h</key>
		<data>
		5fnIS7Tr7E/R0kXpE2Z74e2uGB4=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/Mediation/GADMEnums.h</key>
		<data>
		tJvLWOlUGS/T88+TLEUQKMlOr00=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/Mediation/GADMediatedUnifiedNativeAd.h</key>
		<data>
		/zQcy15VvaZwUYK9vbBuQt8kYMw=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/Mediation/GADMediatedUnifiedNativeAdNotificationSource.h</key>
		<data>
		sRut+yR1ITR2iMoSkRbm452gWnU=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/Mediation/GADMediationAd.h</key>
		<data>
		6EqmzUEtxFunPoFRSaTvHWmwM2g=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/Mediation/GADMediationAdConfiguration.h</key>
		<data>
		z4dRgZl3oSWtJElWBITmwIiAP4w=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/Mediation/GADMediationAdEventDelegate.h</key>
		<data>
		ZqBdeT5cUaS3twsIUjhbpoVlfZE=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/Mediation/GADMediationAdRequest.h</key>
		<data>
		yMjoc9zFKLdSN8U4y1Ew/9c+Qok=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/Mediation/GADMediationAdSize.h</key>
		<data>
		fSUT1kpL5tJuVulCa/ppszvpHe4=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/Mediation/GADMediationAdapter.h</key>
		<data>
		qbJC3o87Nj4opncueh1M2ynCVSg=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/Mediation/GADMediationAppOpenAd.h</key>
		<data>
		DW1HyY7MiRIkXeYLeHZZyJ0hC1U=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/Mediation/GADMediationBannerAd.h</key>
		<data>
		fRuNpE7SjmnAIg8AtxYdcnXlecg=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/Mediation/GADMediationInterstitialAd.h</key>
		<data>
		c7z+4eDsplD/cP+E4R8opNB8q5s=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/Mediation/GADMediationNativeAd.h</key>
		<data>
		Xx7cfccdAUaHeaH9jxLR5YcHnH4=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/Mediation/GADMediationRewardedAd.h</key>
		<data>
		wri4Lk1jBA3XDAFRq19dEh8IsJU=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/Mediation/GADMediationServerConfiguration.h</key>
		<data>
		A54+22v1zABH2pYZw6R1f3iADyA=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/Mediation/GADVersionNumber.h</key>
		<data>
		Q0WWOP9O1Wi+UuOPkTHeSrvHpCU=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/QueryInfo/GADRequest+AdString.h</key>
		<data>
		vcKuPzFb6BRvsJTPBvvY1RrJfds=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/RTBMediation/GADRTBAdapter.h</key>
		<data>
		FQ+EY0l9vQ1iuM0vClDfyG6U5cg=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/RTBMediation/GADRTBRequestParameters.h</key>
		<data>
		IHJsVQZTqqjDy32s9qMZwkytcAY=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Info.plist</key>
		<data>
		+a1GHfOW8+u+fQkY29OCGeofUTU=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Modules/module.modulemap</key>
		<data>
		lfIY8btA05hFyBU2chqKw5NhZLs=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/PrivacyInfo.xcprivacy</key>
		<data>
		VWCA3MeaiPMoOAS1uOteTSLX8js=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>ios-arm64/GoogleMobileAds.framework/GoogleMobileAds</key>
		<dict>
			<key>hash</key>
			<data>
			EL/oX0qz/i7P9efZGIlrz/I2rvc=
			</data>
			<key>hash2</key>
			<data>
			KIheK9iGHafk9GTVd7iEBupvL/pB9obtjYebYHeyGwU=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADAdChoicesPosition.h</key>
		<dict>
			<key>hash</key>
			<data>
			3HUQ7uVcN3WWKscbUgvRTYb6zGU=
			</data>
			<key>hash2</key>
			<data>
			z8RyCmqV5LRQHT/r6A4LKki+a7T7TWM9jZNGV+KjCzM=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADAdChoicesView.h</key>
		<dict>
			<key>hash</key>
			<data>
			qNnvxOY2HmWZxgGuaI6NYKXEFr0=
			</data>
			<key>hash2</key>
			<data>
			0NPaU/FgmojOVQt118d9FloMGFZr9VJw4v8bfJv/Jdk=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADAdFormat.h</key>
		<dict>
			<key>hash</key>
			<data>
			xra0fPg7SwSG7qdLOTU+/Z2/Gzg=
			</data>
			<key>hash2</key>
			<data>
			TJuBo0dcFWS8ULFwnCQrExsOdG7uATkxIvQZshhQNSQ=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADAdLoader.h</key>
		<dict>
			<key>hash</key>
			<data>
			BchePlG8Q1pZwdU4NuK/LPG6BSs=
			</data>
			<key>hash2</key>
			<data>
			CuhBmxxjfuYzA5clBWVdcUdl5vw8tGEZVZ1Rlz1y71c=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADAdLoaderAdTypes.h</key>
		<dict>
			<key>hash</key>
			<data>
			8X8NXKflWuqUW1ZX7DWYTZ1sEJk=
			</data>
			<key>hash2</key>
			<data>
			vQQFoGO3ohgpw50mCQJe3k65L+OO7JbYGOZQ4HFLAsU=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADAdLoaderDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			jsfTiQpvxV8kM+FnNaKpxmMWiJU=
			</data>
			<key>hash2</key>
			<data>
			MbgutmxF+guXHzKBasnTBzbzZme9t8DXa4ZAU4qW+Hg=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADAdMetadata.h</key>
		<dict>
			<key>hash</key>
			<data>
			R+mmMd9BciU+g2LByLSmev5MKeo=
			</data>
			<key>hash2</key>
			<data>
			jqhVfyqTnQTTfMYdg7pZTAglm9HF0FQf370MOS7Av7k=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADAdNetworkExtras.h</key>
		<dict>
			<key>hash</key>
			<data>
			pEhVRx0KAS7ZddDzUBDRkaExh8I=
			</data>
			<key>hash2</key>
			<data>
			1TtxPlH2TflA/HdTtwXXMHzHn3oTG9pujS7dJ+/Df+Q=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADAdReward.h</key>
		<dict>
			<key>hash</key>
			<data>
			jtoiYod9jZUtgHzWgVX6a6E9+ZM=
			</data>
			<key>hash2</key>
			<data>
			N+wlH1X/vtIl8vq1wH71h/J9zxVOWZuchwFZNntHeC0=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADAdSize.h</key>
		<dict>
			<key>hash</key>
			<data>
			uNdf9JCf5I4jdzYwjD7d9tTYjmo=
			</data>
			<key>hash2</key>
			<data>
			UjsL1nyXopJiPvsrx9d0FKMAbLCIUdm/08YimiDUZtE=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADAdSizeDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			zsFshiKVtlod87kG3OVd6NUMOxg=
			</data>
			<key>hash2</key>
			<data>
			LS2WDLHcQDJs1dG33aKk8d17ikg6/dZsEE9h3lZ1PFk=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADAdValue.h</key>
		<dict>
			<key>hash</key>
			<data>
			nkgwpuGaZUS48lKEpALhJFnmgGY=
			</data>
			<key>hash2</key>
			<data>
			9J8OgzwOkEDkxlzUTrmuPpXuLWokkni8bybu4c1RglA=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADAppEventDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			TTXmWnlK+BB7uhMTsV48MDHpkI8=
			</data>
			<key>hash2</key>
			<data>
			WxjpUBycUdhp9DhfJv3HBhkvqzFP7kYO0WngXRzrkok=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADAppOpenAd.h</key>
		<dict>
			<key>hash</key>
			<data>
			Pi+nGzqErKJRbC32MObNfgqE+QQ=
			</data>
			<key>hash2</key>
			<data>
			QtcE+pe8zHLuoso2N6wIrpQdTdi3jCTYCysrfpIYrWA=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADAudioVideoManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			3lS8xsfXwX4ZR6auPLRSpvFrQqQ=
			</data>
			<key>hash2</key>
			<data>
			5J28MGJiIbK7gQnPbnQz7YNN9uK0dFh2AXGkCEt0QGw=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADAudioVideoManagerDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			ZJvHvp3bCw4hRMgzzHqJDvM3HyY=
			</data>
			<key>hash2</key>
			<data>
			ib5UaH4y2caBAI0CmvFotqil2NjDPb7XGrOM6CsyM5c=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADBannerView.h</key>
		<dict>
			<key>hash</key>
			<data>
			dQNnVaOV8vNUGMd/3nGfq1G19e0=
			</data>
			<key>hash2</key>
			<data>
			Kai6yjc/Dtl8cFfWUu0oYXP46wkglZCzAzL5B1vsAOc=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADBannerViewDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			Qrfo7A4zl/bDXNLF7xzuNk/x3+0=
			</data>
			<key>hash2</key>
			<data>
			vw06tNB+9++yLydyfCDfJtRnOu9w375ahQL1LxQkqRE=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADCustomEventBanner.h</key>
		<dict>
			<key>hash</key>
			<data>
			qGCy1A+noahdh79cgrK37Dk1L1E=
			</data>
			<key>hash2</key>
			<data>
			/Kt3PwFV8TseEvh19xGXavmrweaiosLLgkl7yJOmw3Y=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADCustomEventBannerDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			yeVqeb6lj6vnwM+SiT6O4S6UfD0=
			</data>
			<key>hash2</key>
			<data>
			jQQX5f/TTo7cIAqldIeg/3LStODU6LWSQlHaKkOyHKw=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADCustomEventExtras.h</key>
		<dict>
			<key>hash</key>
			<data>
			tyDEfUrnsRXNoYZWtaImmnCDFCk=
			</data>
			<key>hash2</key>
			<data>
			oXUQX/+MlHkuxsl/BRf05X4BTEur0YyWD/uhRImAOws=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADCustomEventInterstitial.h</key>
		<dict>
			<key>hash</key>
			<data>
			l8ZlvOyDlckL0QfqyVbVYPlaNsM=
			</data>
			<key>hash2</key>
			<data>
			y8hqFlSwGmQTGVPJk15HZ9U85gGVL0YdCat9hqZJevs=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADCustomEventInterstitialDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			7ztiyvehcDtHg05srSRIrrztG9o=
			</data>
			<key>hash2</key>
			<data>
			+8sMMN6b4MZCROahpjZoVGMZWA2Og7iQtk/lBQEk6Yw=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADCustomEventNativeAd.h</key>
		<dict>
			<key>hash</key>
			<data>
			M+VlNgp+AUY2mtcxPZ4X/PKpiwc=
			</data>
			<key>hash2</key>
			<data>
			vBHKX+8MHZ3AK16Ilvke0cP1Nx/C3hBkoCypNSsJYT8=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADCustomEventNativeAdDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			8lyeX5v56tQ/QKVA546BH9eHvTg=
			</data>
			<key>hash2</key>
			<data>
			DHQs6U1cgoU7fScPYTl08E3DZHPGYJ3oCfcU5HUgmfE=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADCustomEventParameters.h</key>
		<dict>
			<key>hash</key>
			<data>
			yR6LJNNnOKOLwfRXiLg6xcvILCU=
			</data>
			<key>hash2</key>
			<data>
			QsTX/VbHrb1NLz/mlwFjiBLlC00V9z9+D0RITv0LV+s=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADCustomEventRequest.h</key>
		<dict>
			<key>hash</key>
			<data>
			6LUAOkcdSUtdhOYFQA92wR5aXs0=
			</data>
			<key>hash2</key>
			<data>
			V3yUDsI4AQLfDIa+BfaMILmQz7uy//POEWEZUdA8AdQ=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADCustomNativeAd.h</key>
		<dict>
			<key>hash</key>
			<data>
			tn/GIQH6dk1LLXEU91hDaNPg3Qk=
			</data>
			<key>hash2</key>
			<data>
			DFUC5CDaTaxKWfkneSWyVa02+MJHo5x11ukcIufjTRg=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADCustomNativeAdDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			T3q4J5qaE+fdMt3285vpz2ApyQU=
			</data>
			<key>hash2</key>
			<data>
			c+nafH23J0YGjGtEFOXEA3cJeSwbDd3IhPJdv1/HvSs=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADDebugOptionsViewController.h</key>
		<dict>
			<key>hash</key>
			<data>
			zLjSO2X3zWKSAjU2zL7c1jz4Piw=
			</data>
			<key>hash2</key>
			<data>
			efUPCF1ClQnuaIDQULlcXMbMlWxS0GHYP8KvWthVF84=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADDisplayAdMeasurement.h</key>
		<dict>
			<key>hash</key>
			<data>
			lBOi88QFmY681Nn+rpiQ3AyXmhE=
			</data>
			<key>hash2</key>
			<data>
			7prDgZ8NTese/s3qmdgiHsRYSKH9fh3qqGcDDvQ4V/Q=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADDynamicHeightSearchRequest.h</key>
		<dict>
			<key>hash</key>
			<data>
			ZaF68/opQBrmdxRVGBcjPoilX7I=
			</data>
			<key>hash2</key>
			<data>
			973mu1hx4oo8rYP2Lhw6NMgtaY5il+VqPohAICOpvTw=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADExtras.h</key>
		<dict>
			<key>hash</key>
			<data>
			yYeHP3vuzvjcGhxtTTJ4SWluWdk=
			</data>
			<key>hash2</key>
			<data>
			I+sJaMUeBfZ9l+NFxyBdOctVVlZDmQvgoJNPcJChe1w=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADFullScreenContentDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			+/443o8MPR5e20F7QuE+IUiIyTc=
			</data>
			<key>hash2</key>
			<data>
			g0JJkCqcZuFBMYrTx83/Pg1Jz65N8Zh1CjYG35D4V48=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADInitializationStatus.h</key>
		<dict>
			<key>hash</key>
			<data>
			aDPuW2qEWx4vFqXC8J7su8SS1mw=
			</data>
			<key>hash2</key>
			<data>
			/kRVWmmp9S+lLNPyEqtpx1sU9JXFgqsDLVbfxuJGwu4=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADInterstitialAd.h</key>
		<dict>
			<key>hash</key>
			<data>
			KJTYfV6Ww8kJCuFQ3jSl7FBDHCk=
			</data>
			<key>hash2</key>
			<data>
			b86+ToI9v0lsFPAZAjfB1TmjSagFTTPUotAw8bZyKHM=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADMediaAspectRatio.h</key>
		<dict>
			<key>hash</key>
			<data>
			MzhWPfGm7SG3MHf0XRxd/zOTGK4=
			</data>
			<key>hash2</key>
			<data>
			ZgmtXh3Wm6KsQ1clC0CPMwORURGKWIxEyIxzmyTIYWQ=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADMediaContent.h</key>
		<dict>
			<key>hash</key>
			<data>
			wYEddwgajDVbX61ks28RnXWgMcc=
			</data>
			<key>hash2</key>
			<data>
			KLwsYJ3C2tX1a50Vy20bBjUh04xFCGzt1kfZqAPBDqQ=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADMediaView.h</key>
		<dict>
			<key>hash</key>
			<data>
			TyAN6KtLY2jqOQ/RfcyBfZy6Mak=
			</data>
			<key>hash2</key>
			<data>
			N5r+51BLTsEhJP9GZGh9+P5MVsaKxX3JRwyqYsATtL0=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADMobileAds.h</key>
		<dict>
			<key>hash</key>
			<data>
			IhlvvJHtNFqzRuU86CO7rUFNBpQ=
			</data>
			<key>hash2</key>
			<data>
			+zZoqZUwRjKItHQsBAmWIum9fylCEvBnF5Xw7ghhIXM=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADMultipleAdsAdLoaderOptions.h</key>
		<dict>
			<key>hash</key>
			<data>
			pmnk3VeQeOcbL0PW0hCKfx0MxWc=
			</data>
			<key>hash2</key>
			<data>
			QUFZ5l2UP4/F781+f292FbGVBvWrMX5j8wcGr1UENfQ=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADMuteThisAdReason.h</key>
		<dict>
			<key>hash</key>
			<data>
			5WYeY3zyUAffO7yxDRKUW56PUEE=
			</data>
			<key>hash2</key>
			<data>
			OTl0GW8SEJUhOVAbW4l6ADx6yMLHQ2zOi4kiIRVxoPs=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADNativeAd+ConfirmationClick.h</key>
		<dict>
			<key>hash</key>
			<data>
			tLFTWRd19SRrx9pso51Q//uUP9c=
			</data>
			<key>hash2</key>
			<data>
			l8XM9PQWZswSLJeV0kLGowLGHrw74xxQJM9A58dcoVY=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADNativeAd+CustomClickGesture.h</key>
		<dict>
			<key>hash</key>
			<data>
			SSL/Qe9KWkvYHc7tqXSz8iez+9k=
			</data>
			<key>hash2</key>
			<data>
			Lo4RtYjfhOfhJwkWg5zUIDY78BjkO3ytwpXijlthAcA=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADNativeAd.h</key>
		<dict>
			<key>hash</key>
			<data>
			Q57TQPri0v/hKe8djYpgSUPCYV4=
			</data>
			<key>hash2</key>
			<data>
			ZfhPuBoQmW2Xv3nElDbQ4DgackIwg/+3pq6r3x5juU0=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADNativeAdAssetIdentifiers.h</key>
		<dict>
			<key>hash</key>
			<data>
			WF8V8qBuQYzYube1bpjhk6VTtQc=
			</data>
			<key>hash2</key>
			<data>
			/v1xnzf86+l8Wa+DxIpupxrKmYU4CwQVmR5UFQo5kdU=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADNativeAdCustomClickGestureOptions.h</key>
		<dict>
			<key>hash</key>
			<data>
			zbYVgF8KaoqwycbD78tgtNGzbiY=
			</data>
			<key>hash2</key>
			<data>
			JI559xGzP6c9xdaHVidix1j9lYrcVrZfzYDqubkJT3g=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADNativeAdDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			TS5G14xkP4NoNjiQrUoNBiPC+Qo=
			</data>
			<key>hash2</key>
			<data>
			WIHvaQcAss46qxGupR1IzbRXtu2394EfPrkxOEjPOgo=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADNativeAdImage+Mediation.h</key>
		<dict>
			<key>hash</key>
			<data>
			jERrfqGRcxU8DeM6GzjxB9eP1gQ=
			</data>
			<key>hash2</key>
			<data>
			aIxNwnYa4K1Llc0GbOafG/XcddAmJRTLGdSVo7unsmY=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADNativeAdImage.h</key>
		<dict>
			<key>hash</key>
			<data>
			PItp6/8tb/SyIyliABTwTWso6JQ=
			</data>
			<key>hash2</key>
			<data>
			UA2VqTWnY/vcVmDVeMv5NqxIXBiM8BrPThTylsEigy8=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADNativeAdImageAdLoaderOptions.h</key>
		<dict>
			<key>hash</key>
			<data>
			V+yNFdMJiUaCnKf9t6JhlWj/MEM=
			</data>
			<key>hash2</key>
			<data>
			nCNxXbRhZsg++7MiWrqvzSUA+Lm1u1LkWCrHhrylhlw=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADNativeAdMediaAdLoaderOptions.h</key>
		<dict>
			<key>hash</key>
			<data>
			tQY9a/XhPOV7Tkg3xjrF9iucd0Y=
			</data>
			<key>hash2</key>
			<data>
			fatopw5g0N55T5azCjjGebbWQ5bwzP1GS/oiR+4jyic=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADNativeAdUnconfirmedClickDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			adFUhMad6x1Nszo7jnx/6Go5M64=
			</data>
			<key>hash2</key>
			<data>
			f4NpNGtKRLlHafhMr+J7ShwB5D9Sq4GnhGAaA3StoO4=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADNativeAdViewAdOptions.h</key>
		<dict>
			<key>hash</key>
			<data>
			3qrqu7IZca/R79fLGIOj5kP74pM=
			</data>
			<key>hash2</key>
			<data>
			V5S3Ez5+NfhEBdLiIgRP5SLO79efVcLMB+sZHgCaWaA=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADNativeMuteThisAdLoaderOptions.h</key>
		<dict>
			<key>hash</key>
			<data>
			OMACP/+e4wOxWCxjCBqfbbXvSn8=
			</data>
			<key>hash2</key>
			<data>
			x8pSuscmPucUiXXQ+yD5ySrwf8UOtCDcAlhZ0GBpphg=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADPresentationError.h</key>
		<dict>
			<key>hash</key>
			<data>
			/DwQgJA26rp29qAElahS4M92Ves=
			</data>
			<key>hash2</key>
			<data>
			uJ1MeX9n7DIL5fgshvdtXYcWzbKDN0XQvdGet22tM90=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADQueryInfo.h</key>
		<dict>
			<key>hash</key>
			<data>
			bbx8+sTRZZ18wKFKbdosCh19LP8=
			</data>
			<key>hash2</key>
			<data>
			6NfPyq53QrGwcmPKonb4oeNcf5ojtIdq0xX+QoCaJdc=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADRequest.h</key>
		<dict>
			<key>hash</key>
			<data>
			fA4dVT+ci8eDJOLoX1W56No8Wj8=
			</data>
			<key>hash2</key>
			<data>
			vDQkwq3pf1BgBbuLbz7qsPZut4PxI0A7E4QG/9qkKJA=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADRequestConfiguration.h</key>
		<dict>
			<key>hash</key>
			<data>
			p8xu90L6d/+5xIYqcUedMNMAOas=
			</data>
			<key>hash2</key>
			<data>
			IxXtlmIyfu0lRHSHF2Hw9Xhk/2G576iqrR+RnXC9AFY=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADRequestError.h</key>
		<dict>
			<key>hash</key>
			<data>
			polhNCpFJ9FdSyv9ZH+r8s5tVWA=
			</data>
			<key>hash2</key>
			<data>
			vXCsHuf0QKYAA4kHjKv36GWjET8CJ96zGnroCmrUeEs=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADResponseInfo.h</key>
		<dict>
			<key>hash</key>
			<data>
			nfuBQCjj9cbnaBkHtWmL9khecY4=
			</data>
			<key>hash2</key>
			<data>
			nbQ6jvkpqLuMuOPvuucYgDLgCaxqPN8KaswlVZeAojA=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADRewardedAd.h</key>
		<dict>
			<key>hash</key>
			<data>
			pAkVn1528dLsfgHFDtWtN97HS88=
			</data>
			<key>hash2</key>
			<data>
			FMO180thIMNprVfmhHhm8lxHOqw885TV5A0jbY/IH3I=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADRewardedInterstitialAd.h</key>
		<dict>
			<key>hash</key>
			<data>
			I8l6Sm2AP2znCt+WvYH3rUCNZc4=
			</data>
			<key>hash2</key>
			<data>
			WTlQF7cRit0b88Ice9SeHgBJtHAft5Q4LltzMCAPFZc=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADSearchBannerView.h</key>
		<dict>
			<key>hash</key>
			<data>
			eatzmubDJRMpScDkvBu3MdRUCRc=
			</data>
			<key>hash2</key>
			<data>
			Qa12dme8hovdM1U7WD/HW+1otadM8gaIdKBBruzH/bw=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADServerSideVerificationOptions.h</key>
		<dict>
			<key>hash</key>
			<data>
			rPJOQpfl792Rau0cVjW0tIF0QoM=
			</data>
			<key>hash2</key>
			<data>
			46D6ff/t9sQDyl4eSVbUNdwbP+vgyEVtuU3SoqFGAeU=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADVideoController.h</key>
		<dict>
			<key>hash</key>
			<data>
			ROr6GuDE2MFIjzE8K+EqjJIongA=
			</data>
			<key>hash2</key>
			<data>
			e2vVydSO7vXUNbMM6cihaHU/9a//e/8TA8D8B5DYL8w=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADVideoControllerDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			RPSqq2oNrkWsBiC944aUTYbsGdY=
			</data>
			<key>hash2</key>
			<data>
			9p2dFXDNtLFZwaKZoxKygW8DrRuiqcvwUioovaHH81k=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GADVideoOptions.h</key>
		<dict>
			<key>hash</key>
			<data>
			4gKbOjeYKPDI8y46f7MYmujVolE=
			</data>
			<key>hash2</key>
			<data>
			0eTFepKwZRWtKA/YGqJqxYEY0s4CVqWk9RylHyL59HQ=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GAMBannerView.h</key>
		<dict>
			<key>hash</key>
			<data>
			USGsC8COUC7FEu2FzyjpIu9twkk=
			</data>
			<key>hash2</key>
			<data>
			deHtyNNDaOYXIFzH3jYAIK9mbx2EZaji994mi5j5er8=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GAMBannerViewOptions.h</key>
		<dict>
			<key>hash</key>
			<data>
			UM7nuKLTtZRWtQFxH3g31LWzMUI=
			</data>
			<key>hash2</key>
			<data>
			xy1wdiUTaR9TD/5pA0DbtYhpuHrGV4XFta4AxkWZoo4=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GAMInterstitialAd.h</key>
		<dict>
			<key>hash</key>
			<data>
			Z09wFz3tGqvcJI4/M5Ypp8FFlmI=
			</data>
			<key>hash2</key>
			<data>
			sPKc9CcFtaYep+n0xZA8PA863s9zd1qUuqhe184MrHc=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GAMRequest.h</key>
		<dict>
			<key>hash</key>
			<data>
			PSRXhPJJoqWwh9qC5jGkxT1yU4Q=
			</data>
			<key>hash2</key>
			<data>
			SRELsjt0vZDHb6NEsKWuNkso3kVYWq/GznePWi++MEo=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GoogleMobileAds.h</key>
		<dict>
			<key>hash</key>
			<data>
			ZvHPNB+vixckrnfiuMGTFD0Qdic=
			</data>
			<key>hash2</key>
			<data>
			AYi9Ix7538I7zFQQgQhSSjpdi6d9XYpohczOktlODqs=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/GoogleMobileAdsDefines.h</key>
		<dict>
			<key>hash</key>
			<data>
			ajvTC+Y3CV+fidHio4vx7QWS/6M=
			</data>
			<key>hash2</key>
			<data>
			mnNJ/7LnKHRz/h2wE2x75kgjQeASHdVSG83dWexcL4s=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/Mediation/GADMAdNetworkAdapterProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			m5SQPMUYo8Qedy/ZtOBil8J613s=
			</data>
			<key>hash2</key>
			<data>
			Fq4YSQ553Bjlrfh0wH1Nd52MuOIqrV/ZLVEeOvtY5rI=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/Mediation/GADMAdNetworkConnectorProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			5fnIS7Tr7E/R0kXpE2Z74e2uGB4=
			</data>
			<key>hash2</key>
			<data>
			d2wChA7OxUiBih6OB3xGRfv9d7vDDA0s1dWfnGjCEVQ=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/Mediation/GADMEnums.h</key>
		<dict>
			<key>hash</key>
			<data>
			tJvLWOlUGS/T88+TLEUQKMlOr00=
			</data>
			<key>hash2</key>
			<data>
			aHujPGmJ7ga/uBH0zr4aDGdrs/QWxY+0iivSyhAkyMc=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/Mediation/GADMediatedUnifiedNativeAd.h</key>
		<dict>
			<key>hash</key>
			<data>
			/zQcy15VvaZwUYK9vbBuQt8kYMw=
			</data>
			<key>hash2</key>
			<data>
			m/jsh9VBXQPAkv43geGaps+9AKzK7Txt2JST4dMKsjU=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/Mediation/GADMediatedUnifiedNativeAdNotificationSource.h</key>
		<dict>
			<key>hash</key>
			<data>
			sRut+yR1ITR2iMoSkRbm452gWnU=
			</data>
			<key>hash2</key>
			<data>
			DiABsS9B1hRs7YbArfe7eo8bWXlBSsi5GrsZTAitTjQ=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/Mediation/GADMediationAd.h</key>
		<dict>
			<key>hash</key>
			<data>
			6EqmzUEtxFunPoFRSaTvHWmwM2g=
			</data>
			<key>hash2</key>
			<data>
			qKoEd5XMAhk3sn82Wi4r8Hzd+vcQ7fbKMjbBCH4XPQE=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/Mediation/GADMediationAdConfiguration.h</key>
		<dict>
			<key>hash</key>
			<data>
			z4dRgZl3oSWtJElWBITmwIiAP4w=
			</data>
			<key>hash2</key>
			<data>
			qIgWsRsCufg6ZxJsU0RZBCjgBtzUwFDvE7vJiOXIcXw=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/Mediation/GADMediationAdEventDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			ZqBdeT5cUaS3twsIUjhbpoVlfZE=
			</data>
			<key>hash2</key>
			<data>
			INjxSFNTdlsXyJ845kq1FgnoTF4N+NX0sdT+wURDfQ0=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/Mediation/GADMediationAdRequest.h</key>
		<dict>
			<key>hash</key>
			<data>
			yMjoc9zFKLdSN8U4y1Ew/9c+Qok=
			</data>
			<key>hash2</key>
			<data>
			lbTxotwx/1naFC0gZ3XaC+VvAs/GsiFT694eG7gdlzU=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/Mediation/GADMediationAdSize.h</key>
		<dict>
			<key>hash</key>
			<data>
			fSUT1kpL5tJuVulCa/ppszvpHe4=
			</data>
			<key>hash2</key>
			<data>
			P/JJAuaaOaezk3TdvGU+m33SmMAqF5gmoagU/Lontn8=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/Mediation/GADMediationAdapter.h</key>
		<dict>
			<key>hash</key>
			<data>
			qbJC3o87Nj4opncueh1M2ynCVSg=
			</data>
			<key>hash2</key>
			<data>
			IhSBI3WYdHvqHPdb+ISdS2o6YbRhHLxy5nBjd3cbJ+A=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/Mediation/GADMediationAppOpenAd.h</key>
		<dict>
			<key>hash</key>
			<data>
			DW1HyY7MiRIkXeYLeHZZyJ0hC1U=
			</data>
			<key>hash2</key>
			<data>
			f0jyFcLpNWYrxzAAyl8BkwJxgvsdVXD19xBhh0ksgHo=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/Mediation/GADMediationBannerAd.h</key>
		<dict>
			<key>hash</key>
			<data>
			fRuNpE7SjmnAIg8AtxYdcnXlecg=
			</data>
			<key>hash2</key>
			<data>
			YTD/yrKLZyD4hKhPHp2jBWUYIOkdsEsMns5O0wb2W4A=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/Mediation/GADMediationInterstitialAd.h</key>
		<dict>
			<key>hash</key>
			<data>
			c7z+4eDsplD/cP+E4R8opNB8q5s=
			</data>
			<key>hash2</key>
			<data>
			tDLrlDWc1Z79QLmVD+fpudirmzjwLIVqy9BR2oQ9BUc=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/Mediation/GADMediationNativeAd.h</key>
		<dict>
			<key>hash</key>
			<data>
			Xx7cfccdAUaHeaH9jxLR5YcHnH4=
			</data>
			<key>hash2</key>
			<data>
			5kI5R42wtpp4xwjOvzoQ5Asi2ofPFJcII52EjF2lweA=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/Mediation/GADMediationRewardedAd.h</key>
		<dict>
			<key>hash</key>
			<data>
			wri4Lk1jBA3XDAFRq19dEh8IsJU=
			</data>
			<key>hash2</key>
			<data>
			soB7RxEsM6WEsgqjB3Y+M7TJ29NjKrx1ujyvCDDEy8M=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/Mediation/GADMediationServerConfiguration.h</key>
		<dict>
			<key>hash</key>
			<data>
			A54+22v1zABH2pYZw6R1f3iADyA=
			</data>
			<key>hash2</key>
			<data>
			sjnlGHLJvbmZYbq2PQ9OcLrJjBIb85/mbYpvBn3L37Y=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/Mediation/GADVersionNumber.h</key>
		<dict>
			<key>hash</key>
			<data>
			Q0WWOP9O1Wi+UuOPkTHeSrvHpCU=
			</data>
			<key>hash2</key>
			<data>
			kP8oAojfYr9vrQeLR2FyctlD4tAQ2gSPBbQD/PQTCBs=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/QueryInfo/GADRequest+AdString.h</key>
		<dict>
			<key>hash</key>
			<data>
			vcKuPzFb6BRvsJTPBvvY1RrJfds=
			</data>
			<key>hash2</key>
			<data>
			XtStXDmVf57EYdfLTP2q12x/hBzEkqk6hnFrajPi0fc=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/RTBMediation/GADRTBAdapter.h</key>
		<dict>
			<key>hash</key>
			<data>
			FQ+EY0l9vQ1iuM0vClDfyG6U5cg=
			</data>
			<key>hash2</key>
			<data>
			GxyXjw4yZgFslSDV49ygj6lcIAqD+Gm9KqpbYkg0c6M=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Headers/RTBMediation/GADRTBRequestParameters.h</key>
		<dict>
			<key>hash</key>
			<data>
			IHJsVQZTqqjDy32s9qMZwkytcAY=
			</data>
			<key>hash2</key>
			<data>
			zt7vqwQDhz0Rr/9CgKAI20vd0/N67XpHOh2fQArmtxU=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			f/aILqtfmZaT4TvvRQj6GXwHA2s=
			</data>
			<key>hash2</key>
			<data>
			5LAJBqclhCZNKsGjEmz6Fatuz4rZYrXlv7AbrIp5EEg=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			lfIY8btA05hFyBU2chqKw5NhZLs=
			</data>
			<key>hash2</key>
			<data>
			TsRTUwmz8xyCioZ1RWXLxUHXtYfirRl/DLaDALawjeg=
			</data>
		</dict>
		<key>ios-arm64/GoogleMobileAds.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			VWCA3MeaiPMoOAS1uOteTSLX8js=
			</data>
			<key>hash2</key>
			<data>
			afsRJYL8I/wGxjXJYdJio/e0tWVNKEeHSR3jAA2t+dI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/GoogleMobileAds</key>
		<dict>
			<key>hash</key>
			<data>
			AEzIK7jnYZ9hZfeV5Zo6O+KYlRs=
			</data>
			<key>hash2</key>
			<data>
			jpHzPAJdHVYMpECsl5CAetO505s/VnWHeBpzskFBFoc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADAdChoicesPosition.h</key>
		<dict>
			<key>hash</key>
			<data>
			3HUQ7uVcN3WWKscbUgvRTYb6zGU=
			</data>
			<key>hash2</key>
			<data>
			z8RyCmqV5LRQHT/r6A4LKki+a7T7TWM9jZNGV+KjCzM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADAdChoicesView.h</key>
		<dict>
			<key>hash</key>
			<data>
			qNnvxOY2HmWZxgGuaI6NYKXEFr0=
			</data>
			<key>hash2</key>
			<data>
			0NPaU/FgmojOVQt118d9FloMGFZr9VJw4v8bfJv/Jdk=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADAdFormat.h</key>
		<dict>
			<key>hash</key>
			<data>
			xra0fPg7SwSG7qdLOTU+/Z2/Gzg=
			</data>
			<key>hash2</key>
			<data>
			TJuBo0dcFWS8ULFwnCQrExsOdG7uATkxIvQZshhQNSQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADAdLoader.h</key>
		<dict>
			<key>hash</key>
			<data>
			BchePlG8Q1pZwdU4NuK/LPG6BSs=
			</data>
			<key>hash2</key>
			<data>
			CuhBmxxjfuYzA5clBWVdcUdl5vw8tGEZVZ1Rlz1y71c=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADAdLoaderAdTypes.h</key>
		<dict>
			<key>hash</key>
			<data>
			8X8NXKflWuqUW1ZX7DWYTZ1sEJk=
			</data>
			<key>hash2</key>
			<data>
			vQQFoGO3ohgpw50mCQJe3k65L+OO7JbYGOZQ4HFLAsU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADAdLoaderDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			jsfTiQpvxV8kM+FnNaKpxmMWiJU=
			</data>
			<key>hash2</key>
			<data>
			MbgutmxF+guXHzKBasnTBzbzZme9t8DXa4ZAU4qW+Hg=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADAdMetadata.h</key>
		<dict>
			<key>hash</key>
			<data>
			R+mmMd9BciU+g2LByLSmev5MKeo=
			</data>
			<key>hash2</key>
			<data>
			jqhVfyqTnQTTfMYdg7pZTAglm9HF0FQf370MOS7Av7k=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADAdNetworkExtras.h</key>
		<dict>
			<key>hash</key>
			<data>
			pEhVRx0KAS7ZddDzUBDRkaExh8I=
			</data>
			<key>hash2</key>
			<data>
			1TtxPlH2TflA/HdTtwXXMHzHn3oTG9pujS7dJ+/Df+Q=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADAdReward.h</key>
		<dict>
			<key>hash</key>
			<data>
			jtoiYod9jZUtgHzWgVX6a6E9+ZM=
			</data>
			<key>hash2</key>
			<data>
			N+wlH1X/vtIl8vq1wH71h/J9zxVOWZuchwFZNntHeC0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADAdSize.h</key>
		<dict>
			<key>hash</key>
			<data>
			uNdf9JCf5I4jdzYwjD7d9tTYjmo=
			</data>
			<key>hash2</key>
			<data>
			UjsL1nyXopJiPvsrx9d0FKMAbLCIUdm/08YimiDUZtE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADAdSizeDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			zsFshiKVtlod87kG3OVd6NUMOxg=
			</data>
			<key>hash2</key>
			<data>
			LS2WDLHcQDJs1dG33aKk8d17ikg6/dZsEE9h3lZ1PFk=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADAdValue.h</key>
		<dict>
			<key>hash</key>
			<data>
			nkgwpuGaZUS48lKEpALhJFnmgGY=
			</data>
			<key>hash2</key>
			<data>
			9J8OgzwOkEDkxlzUTrmuPpXuLWokkni8bybu4c1RglA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADAppEventDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			TTXmWnlK+BB7uhMTsV48MDHpkI8=
			</data>
			<key>hash2</key>
			<data>
			WxjpUBycUdhp9DhfJv3HBhkvqzFP7kYO0WngXRzrkok=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADAppOpenAd.h</key>
		<dict>
			<key>hash</key>
			<data>
			Pi+nGzqErKJRbC32MObNfgqE+QQ=
			</data>
			<key>hash2</key>
			<data>
			QtcE+pe8zHLuoso2N6wIrpQdTdi3jCTYCysrfpIYrWA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADAudioVideoManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			3lS8xsfXwX4ZR6auPLRSpvFrQqQ=
			</data>
			<key>hash2</key>
			<data>
			5J28MGJiIbK7gQnPbnQz7YNN9uK0dFh2AXGkCEt0QGw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADAudioVideoManagerDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			ZJvHvp3bCw4hRMgzzHqJDvM3HyY=
			</data>
			<key>hash2</key>
			<data>
			ib5UaH4y2caBAI0CmvFotqil2NjDPb7XGrOM6CsyM5c=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADBannerView.h</key>
		<dict>
			<key>hash</key>
			<data>
			dQNnVaOV8vNUGMd/3nGfq1G19e0=
			</data>
			<key>hash2</key>
			<data>
			Kai6yjc/Dtl8cFfWUu0oYXP46wkglZCzAzL5B1vsAOc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADBannerViewDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			Qrfo7A4zl/bDXNLF7xzuNk/x3+0=
			</data>
			<key>hash2</key>
			<data>
			vw06tNB+9++yLydyfCDfJtRnOu9w375ahQL1LxQkqRE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADCustomEventBanner.h</key>
		<dict>
			<key>hash</key>
			<data>
			qGCy1A+noahdh79cgrK37Dk1L1E=
			</data>
			<key>hash2</key>
			<data>
			/Kt3PwFV8TseEvh19xGXavmrweaiosLLgkl7yJOmw3Y=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADCustomEventBannerDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			yeVqeb6lj6vnwM+SiT6O4S6UfD0=
			</data>
			<key>hash2</key>
			<data>
			jQQX5f/TTo7cIAqldIeg/3LStODU6LWSQlHaKkOyHKw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADCustomEventExtras.h</key>
		<dict>
			<key>hash</key>
			<data>
			tyDEfUrnsRXNoYZWtaImmnCDFCk=
			</data>
			<key>hash2</key>
			<data>
			oXUQX/+MlHkuxsl/BRf05X4BTEur0YyWD/uhRImAOws=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADCustomEventInterstitial.h</key>
		<dict>
			<key>hash</key>
			<data>
			l8ZlvOyDlckL0QfqyVbVYPlaNsM=
			</data>
			<key>hash2</key>
			<data>
			y8hqFlSwGmQTGVPJk15HZ9U85gGVL0YdCat9hqZJevs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADCustomEventInterstitialDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			7ztiyvehcDtHg05srSRIrrztG9o=
			</data>
			<key>hash2</key>
			<data>
			+8sMMN6b4MZCROahpjZoVGMZWA2Og7iQtk/lBQEk6Yw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADCustomEventNativeAd.h</key>
		<dict>
			<key>hash</key>
			<data>
			M+VlNgp+AUY2mtcxPZ4X/PKpiwc=
			</data>
			<key>hash2</key>
			<data>
			vBHKX+8MHZ3AK16Ilvke0cP1Nx/C3hBkoCypNSsJYT8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADCustomEventNativeAdDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			8lyeX5v56tQ/QKVA546BH9eHvTg=
			</data>
			<key>hash2</key>
			<data>
			DHQs6U1cgoU7fScPYTl08E3DZHPGYJ3oCfcU5HUgmfE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADCustomEventParameters.h</key>
		<dict>
			<key>hash</key>
			<data>
			yR6LJNNnOKOLwfRXiLg6xcvILCU=
			</data>
			<key>hash2</key>
			<data>
			QsTX/VbHrb1NLz/mlwFjiBLlC00V9z9+D0RITv0LV+s=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADCustomEventRequest.h</key>
		<dict>
			<key>hash</key>
			<data>
			6LUAOkcdSUtdhOYFQA92wR5aXs0=
			</data>
			<key>hash2</key>
			<data>
			V3yUDsI4AQLfDIa+BfaMILmQz7uy//POEWEZUdA8AdQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADCustomNativeAd.h</key>
		<dict>
			<key>hash</key>
			<data>
			tn/GIQH6dk1LLXEU91hDaNPg3Qk=
			</data>
			<key>hash2</key>
			<data>
			DFUC5CDaTaxKWfkneSWyVa02+MJHo5x11ukcIufjTRg=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADCustomNativeAdDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			T3q4J5qaE+fdMt3285vpz2ApyQU=
			</data>
			<key>hash2</key>
			<data>
			c+nafH23J0YGjGtEFOXEA3cJeSwbDd3IhPJdv1/HvSs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADDebugOptionsViewController.h</key>
		<dict>
			<key>hash</key>
			<data>
			zLjSO2X3zWKSAjU2zL7c1jz4Piw=
			</data>
			<key>hash2</key>
			<data>
			efUPCF1ClQnuaIDQULlcXMbMlWxS0GHYP8KvWthVF84=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADDisplayAdMeasurement.h</key>
		<dict>
			<key>hash</key>
			<data>
			lBOi88QFmY681Nn+rpiQ3AyXmhE=
			</data>
			<key>hash2</key>
			<data>
			7prDgZ8NTese/s3qmdgiHsRYSKH9fh3qqGcDDvQ4V/Q=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADDynamicHeightSearchRequest.h</key>
		<dict>
			<key>hash</key>
			<data>
			ZaF68/opQBrmdxRVGBcjPoilX7I=
			</data>
			<key>hash2</key>
			<data>
			973mu1hx4oo8rYP2Lhw6NMgtaY5il+VqPohAICOpvTw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADExtras.h</key>
		<dict>
			<key>hash</key>
			<data>
			yYeHP3vuzvjcGhxtTTJ4SWluWdk=
			</data>
			<key>hash2</key>
			<data>
			I+sJaMUeBfZ9l+NFxyBdOctVVlZDmQvgoJNPcJChe1w=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADFullScreenContentDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			+/443o8MPR5e20F7QuE+IUiIyTc=
			</data>
			<key>hash2</key>
			<data>
			g0JJkCqcZuFBMYrTx83/Pg1Jz65N8Zh1CjYG35D4V48=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADInitializationStatus.h</key>
		<dict>
			<key>hash</key>
			<data>
			aDPuW2qEWx4vFqXC8J7su8SS1mw=
			</data>
			<key>hash2</key>
			<data>
			/kRVWmmp9S+lLNPyEqtpx1sU9JXFgqsDLVbfxuJGwu4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADInterstitialAd.h</key>
		<dict>
			<key>hash</key>
			<data>
			KJTYfV6Ww8kJCuFQ3jSl7FBDHCk=
			</data>
			<key>hash2</key>
			<data>
			b86+ToI9v0lsFPAZAjfB1TmjSagFTTPUotAw8bZyKHM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADMediaAspectRatio.h</key>
		<dict>
			<key>hash</key>
			<data>
			MzhWPfGm7SG3MHf0XRxd/zOTGK4=
			</data>
			<key>hash2</key>
			<data>
			ZgmtXh3Wm6KsQ1clC0CPMwORURGKWIxEyIxzmyTIYWQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADMediaContent.h</key>
		<dict>
			<key>hash</key>
			<data>
			wYEddwgajDVbX61ks28RnXWgMcc=
			</data>
			<key>hash2</key>
			<data>
			KLwsYJ3C2tX1a50Vy20bBjUh04xFCGzt1kfZqAPBDqQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADMediaView.h</key>
		<dict>
			<key>hash</key>
			<data>
			TyAN6KtLY2jqOQ/RfcyBfZy6Mak=
			</data>
			<key>hash2</key>
			<data>
			N5r+51BLTsEhJP9GZGh9+P5MVsaKxX3JRwyqYsATtL0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADMobileAds.h</key>
		<dict>
			<key>hash</key>
			<data>
			IhlvvJHtNFqzRuU86CO7rUFNBpQ=
			</data>
			<key>hash2</key>
			<data>
			+zZoqZUwRjKItHQsBAmWIum9fylCEvBnF5Xw7ghhIXM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADMultipleAdsAdLoaderOptions.h</key>
		<dict>
			<key>hash</key>
			<data>
			pmnk3VeQeOcbL0PW0hCKfx0MxWc=
			</data>
			<key>hash2</key>
			<data>
			QUFZ5l2UP4/F781+f292FbGVBvWrMX5j8wcGr1UENfQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADMuteThisAdReason.h</key>
		<dict>
			<key>hash</key>
			<data>
			5WYeY3zyUAffO7yxDRKUW56PUEE=
			</data>
			<key>hash2</key>
			<data>
			OTl0GW8SEJUhOVAbW4l6ADx6yMLHQ2zOi4kiIRVxoPs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADNativeAd+ConfirmationClick.h</key>
		<dict>
			<key>hash</key>
			<data>
			tLFTWRd19SRrx9pso51Q//uUP9c=
			</data>
			<key>hash2</key>
			<data>
			l8XM9PQWZswSLJeV0kLGowLGHrw74xxQJM9A58dcoVY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADNativeAd+CustomClickGesture.h</key>
		<dict>
			<key>hash</key>
			<data>
			SSL/Qe9KWkvYHc7tqXSz8iez+9k=
			</data>
			<key>hash2</key>
			<data>
			Lo4RtYjfhOfhJwkWg5zUIDY78BjkO3ytwpXijlthAcA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADNativeAd.h</key>
		<dict>
			<key>hash</key>
			<data>
			Q57TQPri0v/hKe8djYpgSUPCYV4=
			</data>
			<key>hash2</key>
			<data>
			ZfhPuBoQmW2Xv3nElDbQ4DgackIwg/+3pq6r3x5juU0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADNativeAdAssetIdentifiers.h</key>
		<dict>
			<key>hash</key>
			<data>
			WF8V8qBuQYzYube1bpjhk6VTtQc=
			</data>
			<key>hash2</key>
			<data>
			/v1xnzf86+l8Wa+DxIpupxrKmYU4CwQVmR5UFQo5kdU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADNativeAdCustomClickGestureOptions.h</key>
		<dict>
			<key>hash</key>
			<data>
			zbYVgF8KaoqwycbD78tgtNGzbiY=
			</data>
			<key>hash2</key>
			<data>
			JI559xGzP6c9xdaHVidix1j9lYrcVrZfzYDqubkJT3g=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADNativeAdDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			TS5G14xkP4NoNjiQrUoNBiPC+Qo=
			</data>
			<key>hash2</key>
			<data>
			WIHvaQcAss46qxGupR1IzbRXtu2394EfPrkxOEjPOgo=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADNativeAdImage+Mediation.h</key>
		<dict>
			<key>hash</key>
			<data>
			jERrfqGRcxU8DeM6GzjxB9eP1gQ=
			</data>
			<key>hash2</key>
			<data>
			aIxNwnYa4K1Llc0GbOafG/XcddAmJRTLGdSVo7unsmY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADNativeAdImage.h</key>
		<dict>
			<key>hash</key>
			<data>
			PItp6/8tb/SyIyliABTwTWso6JQ=
			</data>
			<key>hash2</key>
			<data>
			UA2VqTWnY/vcVmDVeMv5NqxIXBiM8BrPThTylsEigy8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADNativeAdImageAdLoaderOptions.h</key>
		<dict>
			<key>hash</key>
			<data>
			V+yNFdMJiUaCnKf9t6JhlWj/MEM=
			</data>
			<key>hash2</key>
			<data>
			nCNxXbRhZsg++7MiWrqvzSUA+Lm1u1LkWCrHhrylhlw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADNativeAdMediaAdLoaderOptions.h</key>
		<dict>
			<key>hash</key>
			<data>
			tQY9a/XhPOV7Tkg3xjrF9iucd0Y=
			</data>
			<key>hash2</key>
			<data>
			fatopw5g0N55T5azCjjGebbWQ5bwzP1GS/oiR+4jyic=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADNativeAdUnconfirmedClickDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			adFUhMad6x1Nszo7jnx/6Go5M64=
			</data>
			<key>hash2</key>
			<data>
			f4NpNGtKRLlHafhMr+J7ShwB5D9Sq4GnhGAaA3StoO4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADNativeAdViewAdOptions.h</key>
		<dict>
			<key>hash</key>
			<data>
			3qrqu7IZca/R79fLGIOj5kP74pM=
			</data>
			<key>hash2</key>
			<data>
			V5S3Ez5+NfhEBdLiIgRP5SLO79efVcLMB+sZHgCaWaA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADNativeMuteThisAdLoaderOptions.h</key>
		<dict>
			<key>hash</key>
			<data>
			OMACP/+e4wOxWCxjCBqfbbXvSn8=
			</data>
			<key>hash2</key>
			<data>
			x8pSuscmPucUiXXQ+yD5ySrwf8UOtCDcAlhZ0GBpphg=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADPresentationError.h</key>
		<dict>
			<key>hash</key>
			<data>
			/DwQgJA26rp29qAElahS4M92Ves=
			</data>
			<key>hash2</key>
			<data>
			uJ1MeX9n7DIL5fgshvdtXYcWzbKDN0XQvdGet22tM90=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADQueryInfo.h</key>
		<dict>
			<key>hash</key>
			<data>
			bbx8+sTRZZ18wKFKbdosCh19LP8=
			</data>
			<key>hash2</key>
			<data>
			6NfPyq53QrGwcmPKonb4oeNcf5ojtIdq0xX+QoCaJdc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADRequest.h</key>
		<dict>
			<key>hash</key>
			<data>
			fA4dVT+ci8eDJOLoX1W56No8Wj8=
			</data>
			<key>hash2</key>
			<data>
			vDQkwq3pf1BgBbuLbz7qsPZut4PxI0A7E4QG/9qkKJA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADRequestConfiguration.h</key>
		<dict>
			<key>hash</key>
			<data>
			p8xu90L6d/+5xIYqcUedMNMAOas=
			</data>
			<key>hash2</key>
			<data>
			IxXtlmIyfu0lRHSHF2Hw9Xhk/2G576iqrR+RnXC9AFY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADRequestError.h</key>
		<dict>
			<key>hash</key>
			<data>
			polhNCpFJ9FdSyv9ZH+r8s5tVWA=
			</data>
			<key>hash2</key>
			<data>
			vXCsHuf0QKYAA4kHjKv36GWjET8CJ96zGnroCmrUeEs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADResponseInfo.h</key>
		<dict>
			<key>hash</key>
			<data>
			nfuBQCjj9cbnaBkHtWmL9khecY4=
			</data>
			<key>hash2</key>
			<data>
			nbQ6jvkpqLuMuOPvuucYgDLgCaxqPN8KaswlVZeAojA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADRewardedAd.h</key>
		<dict>
			<key>hash</key>
			<data>
			pAkVn1528dLsfgHFDtWtN97HS88=
			</data>
			<key>hash2</key>
			<data>
			FMO180thIMNprVfmhHhm8lxHOqw885TV5A0jbY/IH3I=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADRewardedInterstitialAd.h</key>
		<dict>
			<key>hash</key>
			<data>
			I8l6Sm2AP2znCt+WvYH3rUCNZc4=
			</data>
			<key>hash2</key>
			<data>
			WTlQF7cRit0b88Ice9SeHgBJtHAft5Q4LltzMCAPFZc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADSearchBannerView.h</key>
		<dict>
			<key>hash</key>
			<data>
			eatzmubDJRMpScDkvBu3MdRUCRc=
			</data>
			<key>hash2</key>
			<data>
			Qa12dme8hovdM1U7WD/HW+1otadM8gaIdKBBruzH/bw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADServerSideVerificationOptions.h</key>
		<dict>
			<key>hash</key>
			<data>
			rPJOQpfl792Rau0cVjW0tIF0QoM=
			</data>
			<key>hash2</key>
			<data>
			46D6ff/t9sQDyl4eSVbUNdwbP+vgyEVtuU3SoqFGAeU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADVideoController.h</key>
		<dict>
			<key>hash</key>
			<data>
			ROr6GuDE2MFIjzE8K+EqjJIongA=
			</data>
			<key>hash2</key>
			<data>
			e2vVydSO7vXUNbMM6cihaHU/9a//e/8TA8D8B5DYL8w=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADVideoControllerDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			RPSqq2oNrkWsBiC944aUTYbsGdY=
			</data>
			<key>hash2</key>
			<data>
			9p2dFXDNtLFZwaKZoxKygW8DrRuiqcvwUioovaHH81k=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GADVideoOptions.h</key>
		<dict>
			<key>hash</key>
			<data>
			4gKbOjeYKPDI8y46f7MYmujVolE=
			</data>
			<key>hash2</key>
			<data>
			0eTFepKwZRWtKA/YGqJqxYEY0s4CVqWk9RylHyL59HQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GAMBannerView.h</key>
		<dict>
			<key>hash</key>
			<data>
			USGsC8COUC7FEu2FzyjpIu9twkk=
			</data>
			<key>hash2</key>
			<data>
			deHtyNNDaOYXIFzH3jYAIK9mbx2EZaji994mi5j5er8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GAMBannerViewOptions.h</key>
		<dict>
			<key>hash</key>
			<data>
			UM7nuKLTtZRWtQFxH3g31LWzMUI=
			</data>
			<key>hash2</key>
			<data>
			xy1wdiUTaR9TD/5pA0DbtYhpuHrGV4XFta4AxkWZoo4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GAMInterstitialAd.h</key>
		<dict>
			<key>hash</key>
			<data>
			Z09wFz3tGqvcJI4/M5Ypp8FFlmI=
			</data>
			<key>hash2</key>
			<data>
			sPKc9CcFtaYep+n0xZA8PA863s9zd1qUuqhe184MrHc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GAMRequest.h</key>
		<dict>
			<key>hash</key>
			<data>
			PSRXhPJJoqWwh9qC5jGkxT1yU4Q=
			</data>
			<key>hash2</key>
			<data>
			SRELsjt0vZDHb6NEsKWuNkso3kVYWq/GznePWi++MEo=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GoogleMobileAds.h</key>
		<dict>
			<key>hash</key>
			<data>
			ZvHPNB+vixckrnfiuMGTFD0Qdic=
			</data>
			<key>hash2</key>
			<data>
			AYi9Ix7538I7zFQQgQhSSjpdi6d9XYpohczOktlODqs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/GoogleMobileAdsDefines.h</key>
		<dict>
			<key>hash</key>
			<data>
			ajvTC+Y3CV+fidHio4vx7QWS/6M=
			</data>
			<key>hash2</key>
			<data>
			mnNJ/7LnKHRz/h2wE2x75kgjQeASHdVSG83dWexcL4s=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/Mediation/GADMAdNetworkAdapterProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			m5SQPMUYo8Qedy/ZtOBil8J613s=
			</data>
			<key>hash2</key>
			<data>
			Fq4YSQ553Bjlrfh0wH1Nd52MuOIqrV/ZLVEeOvtY5rI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/Mediation/GADMAdNetworkConnectorProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			5fnIS7Tr7E/R0kXpE2Z74e2uGB4=
			</data>
			<key>hash2</key>
			<data>
			d2wChA7OxUiBih6OB3xGRfv9d7vDDA0s1dWfnGjCEVQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/Mediation/GADMEnums.h</key>
		<dict>
			<key>hash</key>
			<data>
			tJvLWOlUGS/T88+TLEUQKMlOr00=
			</data>
			<key>hash2</key>
			<data>
			aHujPGmJ7ga/uBH0zr4aDGdrs/QWxY+0iivSyhAkyMc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/Mediation/GADMediatedUnifiedNativeAd.h</key>
		<dict>
			<key>hash</key>
			<data>
			/zQcy15VvaZwUYK9vbBuQt8kYMw=
			</data>
			<key>hash2</key>
			<data>
			m/jsh9VBXQPAkv43geGaps+9AKzK7Txt2JST4dMKsjU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/Mediation/GADMediatedUnifiedNativeAdNotificationSource.h</key>
		<dict>
			<key>hash</key>
			<data>
			sRut+yR1ITR2iMoSkRbm452gWnU=
			</data>
			<key>hash2</key>
			<data>
			DiABsS9B1hRs7YbArfe7eo8bWXlBSsi5GrsZTAitTjQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/Mediation/GADMediationAd.h</key>
		<dict>
			<key>hash</key>
			<data>
			6EqmzUEtxFunPoFRSaTvHWmwM2g=
			</data>
			<key>hash2</key>
			<data>
			qKoEd5XMAhk3sn82Wi4r8Hzd+vcQ7fbKMjbBCH4XPQE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/Mediation/GADMediationAdConfiguration.h</key>
		<dict>
			<key>hash</key>
			<data>
			z4dRgZl3oSWtJElWBITmwIiAP4w=
			</data>
			<key>hash2</key>
			<data>
			qIgWsRsCufg6ZxJsU0RZBCjgBtzUwFDvE7vJiOXIcXw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/Mediation/GADMediationAdEventDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			ZqBdeT5cUaS3twsIUjhbpoVlfZE=
			</data>
			<key>hash2</key>
			<data>
			INjxSFNTdlsXyJ845kq1FgnoTF4N+NX0sdT+wURDfQ0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/Mediation/GADMediationAdRequest.h</key>
		<dict>
			<key>hash</key>
			<data>
			yMjoc9zFKLdSN8U4y1Ew/9c+Qok=
			</data>
			<key>hash2</key>
			<data>
			lbTxotwx/1naFC0gZ3XaC+VvAs/GsiFT694eG7gdlzU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/Mediation/GADMediationAdSize.h</key>
		<dict>
			<key>hash</key>
			<data>
			fSUT1kpL5tJuVulCa/ppszvpHe4=
			</data>
			<key>hash2</key>
			<data>
			P/JJAuaaOaezk3TdvGU+m33SmMAqF5gmoagU/Lontn8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/Mediation/GADMediationAdapter.h</key>
		<dict>
			<key>hash</key>
			<data>
			qbJC3o87Nj4opncueh1M2ynCVSg=
			</data>
			<key>hash2</key>
			<data>
			IhSBI3WYdHvqHPdb+ISdS2o6YbRhHLxy5nBjd3cbJ+A=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/Mediation/GADMediationAppOpenAd.h</key>
		<dict>
			<key>hash</key>
			<data>
			DW1HyY7MiRIkXeYLeHZZyJ0hC1U=
			</data>
			<key>hash2</key>
			<data>
			f0jyFcLpNWYrxzAAyl8BkwJxgvsdVXD19xBhh0ksgHo=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/Mediation/GADMediationBannerAd.h</key>
		<dict>
			<key>hash</key>
			<data>
			fRuNpE7SjmnAIg8AtxYdcnXlecg=
			</data>
			<key>hash2</key>
			<data>
			YTD/yrKLZyD4hKhPHp2jBWUYIOkdsEsMns5O0wb2W4A=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/Mediation/GADMediationInterstitialAd.h</key>
		<dict>
			<key>hash</key>
			<data>
			c7z+4eDsplD/cP+E4R8opNB8q5s=
			</data>
			<key>hash2</key>
			<data>
			tDLrlDWc1Z79QLmVD+fpudirmzjwLIVqy9BR2oQ9BUc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/Mediation/GADMediationNativeAd.h</key>
		<dict>
			<key>hash</key>
			<data>
			Xx7cfccdAUaHeaH9jxLR5YcHnH4=
			</data>
			<key>hash2</key>
			<data>
			5kI5R42wtpp4xwjOvzoQ5Asi2ofPFJcII52EjF2lweA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/Mediation/GADMediationRewardedAd.h</key>
		<dict>
			<key>hash</key>
			<data>
			wri4Lk1jBA3XDAFRq19dEh8IsJU=
			</data>
			<key>hash2</key>
			<data>
			soB7RxEsM6WEsgqjB3Y+M7TJ29NjKrx1ujyvCDDEy8M=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/Mediation/GADMediationServerConfiguration.h</key>
		<dict>
			<key>hash</key>
			<data>
			A54+22v1zABH2pYZw6R1f3iADyA=
			</data>
			<key>hash2</key>
			<data>
			sjnlGHLJvbmZYbq2PQ9OcLrJjBIb85/mbYpvBn3L37Y=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/Mediation/GADVersionNumber.h</key>
		<dict>
			<key>hash</key>
			<data>
			Q0WWOP9O1Wi+UuOPkTHeSrvHpCU=
			</data>
			<key>hash2</key>
			<data>
			kP8oAojfYr9vrQeLR2FyctlD4tAQ2gSPBbQD/PQTCBs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/QueryInfo/GADRequest+AdString.h</key>
		<dict>
			<key>hash</key>
			<data>
			vcKuPzFb6BRvsJTPBvvY1RrJfds=
			</data>
			<key>hash2</key>
			<data>
			XtStXDmVf57EYdfLTP2q12x/hBzEkqk6hnFrajPi0fc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/RTBMediation/GADRTBAdapter.h</key>
		<dict>
			<key>hash</key>
			<data>
			FQ+EY0l9vQ1iuM0vClDfyG6U5cg=
			</data>
			<key>hash2</key>
			<data>
			GxyXjw4yZgFslSDV49ygj6lcIAqD+Gm9KqpbYkg0c6M=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Headers/RTBMediation/GADRTBRequestParameters.h</key>
		<dict>
			<key>hash</key>
			<data>
			IHJsVQZTqqjDy32s9qMZwkytcAY=
			</data>
			<key>hash2</key>
			<data>
			zt7vqwQDhz0Rr/9CgKAI20vd0/N67XpHOh2fQArmtxU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			+a1GHfOW8+u+fQkY29OCGeofUTU=
			</data>
			<key>hash2</key>
			<data>
			jRszgh3PEYMmqI+MNFlLO0GdPk9Hykh3SVSoeuv/4VI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			lfIY8btA05hFyBU2chqKw5NhZLs=
			</data>
			<key>hash2</key>
			<data>
			TsRTUwmz8xyCioZ1RWXLxUHXtYfirRl/DLaDALawjeg=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleMobileAds.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			VWCA3MeaiPMoOAS1uOteTSLX8js=
			</data>
			<key>hash2</key>
			<data>
			afsRJYL8I/wGxjXJYdJio/e0tWVNKEeHSR3jAA2t+dI=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
