{"name": "EAFeatureGuideView", "version": "1.0.9", "summary": "Relayout after iOS device did changed Orientation.", "description": "An easy way to show feature guide with EAFeatureGuideView for iOS apps.", "homepage": "https://github.com/Easence/EAFeatureGuideView", "license": {"type": "MIT", "file": "LICENSE"}, "authors": {"Easence": "yiyunh<PERSON>@gmail.com"}, "platforms": {"ios": "7.0"}, "source": {"git": "https://github.com/Easence/EAFeatureGuideView.git", "tag": "1.0.9"}, "source_files": ["EAFeatureGuideView", "EAFeatureGuideView/**/*.{h,m}"], "resources": "EAFeatureGuideView/Resources/*.png", "frameworks": "UIKit"}